package com.example.calling_agent_app

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.telephony.TelephonyManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CALL_END_CHANNEL = "call_end_detection"
    private var callEndReceiver: CallEndReceiver? = null
    private var methodChannel: MethodChannel? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CALL_END_CHANNEL)
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "startListening" -> {
                    startCallEndDetection()
                    result.success(null)
                }
                "stopListening" -> {
                    stopCallEndDetection()
                    result.success(null)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun startCallEndDetection() {
        if (callEndReceiver == null) {
            callEndReceiver = CallEndReceiver(methodChannel)
            val filter = IntentFilter().apply {
                addAction(TelephonyManager.ACTION_PHONE_STATE_CHANGED)
            }
            registerReceiver(callEndReceiver, filter)
        }
    }

    private fun stopCallEndDetection() {
        callEndReceiver?.let {
            unregisterReceiver(it)
            callEndReceiver = null
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopCallEndDetection()
    }
}
