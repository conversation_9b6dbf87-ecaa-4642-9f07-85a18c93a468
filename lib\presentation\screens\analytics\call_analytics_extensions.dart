import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../data/models/call_log_model.dart';

// Extension methods for Call Analytics Screen
extension CallAnalyticsExtensions on State {
  
  Widget buildCallVolumeHeatmap(List<CallLogModel> logs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Call Volume Heatmap',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Day of week vs Hour of day',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 20),
          _buildHeatmapGrid(logs),
        ],
      ),
    );
  }

  Widget _buildHeatmapGrid(List<CallLogModel> logs) {
    final heatmapData = _calculateHeatmapData(logs);
    final maxValue = heatmapData.values.isEmpty ? 1 : heatmapData.values.reduce((a, b) => a > b ? a : b);
    
    return Column(
      children: [
        // Hour labels
        Row(
          children: [
            const SizedBox(width: 40), // Space for day labels
            ...List.generate(6, (index) {
              final hour = index * 4;
              return Expanded(
                child: Center(
                  child: Text(
                    _formatHourShort(hour),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
        const SizedBox(height: 8),
        // Heatmap grid
        ...List.generate(7, (dayIndex) {
          final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                SizedBox(
                  width: 40,
                  child: Text(
                    dayNames[dayIndex],
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
                ...List.generate(24, (hourIndex) {
                  final key = '${dayIndex + 1}-$hourIndex';
                  final value = heatmapData[key] ?? 0;
                  final intensity = maxValue > 0 ? value / maxValue : 0.0;
                  
                  return Expanded(
                    child: Container(
                      height: 20,
                      margin: const EdgeInsets.all(1),
                      decoration: BoxDecoration(
                        color: _getHeatmapColor(intensity),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: value > 0 ? Center(
                        child: Text(
                          value.toString(),
                          style: TextStyle(
                            fontSize: 8,
                            color: intensity > 0.5 ? Colors.white : Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ) : null,
                    ),
                  );
                }),
              ],
            ),
          );
        }),
        const SizedBox(height: 12),
        _buildHeatmapLegend(),
      ],
    );
  }

  Map<String, int> _calculateHeatmapData(List<CallLogModel> logs) {
    final heatmapData = <String, int>{};
    
    for (final log in logs) {
      final dayOfWeek = log.timestamp.weekday;
      final hour = log.timestamp.hour;
      final key = '$dayOfWeek-$hour';
      
      heatmapData[key] = (heatmapData[key] ?? 0) + 1;
    }
    
    return heatmapData;
  }

  Color _getHeatmapColor(double intensity) {
    if (intensity == 0) return Colors.grey.shade100;
    if (intensity < 0.2) return Colors.blue.shade100;
    if (intensity < 0.4) return Colors.blue.shade200;
    if (intensity < 0.6) return Colors.blue.shade400;
    if (intensity < 0.8) return Colors.blue.shade600;
    return Colors.blue.shade800;
  }

  Widget _buildHeatmapLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Less',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(width: 8),
        ...List.generate(5, (index) {
          return Container(
            width: 12,
            height: 12,
            margin: const EdgeInsets.symmetric(horizontal: 1),
            decoration: BoxDecoration(
              color: _getHeatmapColor((index + 1) / 5),
              borderRadius: BorderRadius.circular(2),
            ),
          );
        }),
        const SizedBox(width: 8),
        Text(
          'More',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  String _formatHourShort(int hour) {
    if (hour == 0) return '12a';
    if (hour < 12) return '${hour}a';
    if (hour == 12) return '12p';
    return '${hour - 12}p';
  }

  Widget buildDurationByTimeAnalysis(List<CallLogModel> logs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Average Call Duration by Time',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                gridData: FlGridData(show: false),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          '${value.toInt()}m',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        );
                      },
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      getTitlesWidget: (value, meta) {
                        final timeSlots = ['Morning', 'Afternoon', 'Evening', 'Night'];
                        final index = value.toInt();
                        if (index >= 0 && index < timeSlots.length) {
                          return Text(
                            timeSlots[index],
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false),
                barGroups: _getDurationByTimeBarGroups(logs),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<BarChartGroupData> _getDurationByTimeBarGroups(List<CallLogModel> logs) {
    final timeSlotDurations = _calculateTimeSlotDurations(logs);
    
    return timeSlotDurations.entries.map((entry) {
      return BarChartGroupData(
        x: entry.key,
        barRods: [
          BarChartRodData(
            toY: entry.value,
            color: _getTimeSlotColor(entry.key),
            width: 40,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();
  }

  Map<int, double> _calculateTimeSlotDurations(List<CallLogModel> logs) {
    final timeSlots = <int, List<int>>{
      0: [], // Morning (6-12)
      1: [], // Afternoon (12-18)
      2: [], // Evening (18-22)
      3: [], // Night (22-6)
    };
    
    for (final log in logs) {
      if (log.duration > 0) {
        final hour = log.timestamp.hour;
        int slot;
        if (hour >= 6 && hour < 12) slot = 0;
        else if (hour >= 12 && hour < 18) slot = 1;
        else if (hour >= 18 && hour < 22) slot = 2;
        else slot = 3;
        
        timeSlots[slot]!.add(log.duration);
      }
    }
    
    final averages = <int, double>{};
    for (final entry in timeSlots.entries) {
      if (entry.value.isNotEmpty) {
        final avgSeconds = entry.value.reduce((a, b) => a + b) / entry.value.length;
        averages[entry.key] = avgSeconds / 60; // Convert to minutes
      } else {
        averages[entry.key] = 0.0;
      }
    }
    
    return averages;
  }

  Color _getTimeSlotColor(int slot) {
    switch (slot) {
      case 0: return Colors.orange; // Morning
      case 1: return Colors.blue; // Afternoon
      case 2: return Colors.green; // Evening
      case 3: return Colors.purple; // Night
      default: return Colors.grey;
    }
  }
}
