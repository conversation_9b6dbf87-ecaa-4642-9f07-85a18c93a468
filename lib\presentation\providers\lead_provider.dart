import 'package:flutter/material.dart';
import '../../data/models/lead_model.dart';
import '../../data/services/api_service.dart';
import 'package:calling_agent_app/data/services/notification_service.dart';

import '../../data/services/local_storage_service.dart';

class LeadProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final LocalStorageService _localStorage = LocalStorageService();
  final NotificationService _notificationService = NotificationService();

  List<LeadModel> _leads = [];
  List<LeadModel> _filteredLeads = [];
  bool _isLoading = false;
  String _searchQuery = '';
  LeadStatus? _selectedStatus;

  List<LeadModel> get leads => _leads;
  List<LeadModel> get filteredLeads => _filteredLeads;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  LeadStatus? get selectedStatus => _selectedStatus;

  Future<void> loadLeads() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Load from local storage first
      final localLeads = await _localStorage.getLeads();
      _leads = localLeads;
      _applyFilters();

      // Then try to load from API
      await _loadFromAPI();
    } catch (e) {
      debugPrint('Error loading leads: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _loadFromAPI() async {
    try {
      final apiLeads = await _apiService.getLeads();

      // Merge server leads with local unsynced leads
      final localUnsyncedLeads =
          _leads.where((lead) => !lead.isSynced).toList();

      // Start with server leads (they are synced)
      final mergedLeads = <LeadModel>[...apiLeads];

      // Add local unsynced leads that don't exist on server
      for (final localLead in localUnsyncedLeads) {
        final existsOnServer = apiLeads.any((serverLead) =>
            serverLead.phone == localLead.phone &&
            serverLead.name == localLead.name);

        if (!existsOnServer) {
          mergedLeads.insert(0, localLead); // Add at beginning for newest first
        }
      }

      _leads = mergedLeads;

      await _localStorage.saveLeads(_leads);
      _applyFilters();
    } catch (e) {
      // If API fails, keep local leads only
      debugPrint('Error loading leads from API: $e');
    }
  }

  Future<void> addLead(LeadModel lead) async {
    try {
      // Add to local list first
      _leads.insert(0, lead);
      _applyFilters();
      notifyListeners();

      // Save to local storage
      await _localStorage.saveLeads(_leads);

      // Don't sync to server immediately - let user control sync
      debugPrint('Lead added locally: ${lead.name}');
    } catch (e) {
      debugPrint('Error adding lead: $e');
    }
  }

  // Add lead from call classification (local only initially)
  Future<void> addLeadFromClassification(LeadModel lead) async {
    try {
      // Mark as local/unsynced lead
      final localLead = lead.copyWith(
        isSynced: false,
        source: 'Call Classification',
      );

      // Add to local list first
      _leads.insert(0, localLead);
      _applyFilters();
      notifyListeners();

      // Save to local storage
      await _localStorage.saveLeads(_leads);

      debugPrint(
          'Lead from call classification added locally: ${localLead.name}');
    } catch (e) {
      debugPrint('Error adding lead from classification: $e');
    }
  }

  // Sync local leads with server
  Future<void> syncLocalLeads() async {
    try {
      final unsyncedLeads = _leads.where((lead) => !lead.isSynced).toList();

      for (final lead in unsyncedLeads) {
        try {
          final savedLead = await _apiService.createLead(lead);

          // Replace local lead with server response
          final index = _leads.indexWhere((l) => l.id == lead.id);
          if (index != -1) {
            _leads[index] = savedLead.copyWith(isSynced: true);
          }

          debugPrint('Lead synced to server: ${savedLead.name}');
        } catch (e) {
          debugPrint('Error syncing lead ${lead.name}: $e');
        }
      }

      // Save updated leads to local storage
      await _localStorage.saveLeads(_leads);
      _applyFilters();
      notifyListeners();
    } catch (e) {
      debugPrint('Error syncing leads: $e');
    }
  }

  Future<void> updateLead(LeadModel updatedLead) async {
    try {
      // Update local list
      final index = _leads.indexWhere((lead) => lead.id == updatedLead.id);
      if (index != -1) {
        _leads[index] = updatedLead;
        _applyFilters();
        notifyListeners();

        // Save to local storage
        await _localStorage.saveLeads(_leads);

        // Try to update on API
        try {
          await _apiService.updateLead(updatedLead.id, updatedLead.toJson());
          if (updatedLead.followUpDate != null) {
            _notificationService.showNotification(
              'Follow-up Reminder',
              'You have a follow-up with ${updatedLead.name} at ${updatedLead.followUpDate}',
            );
          }
        } catch (e) {
          debugPrint('Error updating lead on API: $e');
        }
      }
    } catch (e) {
      debugPrint('Error updating lead: $e');
    }
  }

  Future<void> deleteLead(String leadId) async {
    try {
      _leads.removeWhere((lead) => lead.id == leadId);
      _applyFilters();
      notifyListeners();

      await _localStorage.saveLeads(_leads);

      // TODO: Add API call to delete lead
    } catch (e) {
      debugPrint('Error deleting lead: $e');
    }
  }

  void searchLeads(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  void filterByStatus(LeadStatus? status) {
    _selectedStatus = status;
    _applyFilters();
    notifyListeners();
  }

  void _applyFilters() {
    List<LeadModel> filtered = List.from(_leads);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((lead) =>
              lead.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              lead.phone.contains(_searchQuery) ||
              (lead.email?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
                  false))
          .toList();
    }

    // Apply status filter
    if (_selectedStatus != null) {
      filtered =
          filtered.where((lead) => lead.status == _selectedStatus).toList();
    }

    _filteredLeads = filtered;
  }

  Map<String, dynamic> getLeadStats() {
    final total = _leads.length;
    final newLeads =
        _leads.where((lead) => lead.status == LeadStatus.new_lead).length;
    final contacted =
        _leads.where((lead) => lead.status == LeadStatus.contacted).length;
    final qualified =
        _leads.where((lead) => lead.status == LeadStatus.qualified).length;
    final closedWon =
        _leads.where((lead) => lead.status == LeadStatus.closed_won).length;
    final closedLost =
        _leads.where((lead) => lead.status == LeadStatus.closed_lost).length;

    // Follow-ups due today
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    final followUps = _leads
        .where((lead) =>
            lead.followUpDate != null &&
            lead.followUpDate!.isAfter(todayStart) &&
            lead.followUpDate!.isBefore(todayEnd))
        .length;

    return {
      'total': total,
      'newLeads': newLeads,
      'contacted': contacted,
      'qualified': qualified,
      'closedWon': closedWon,
      'closedLost': closedLost,
      'followUps': followUps,
      'conversionRate':
          total > 0 ? (closedWon / total * 100).toStringAsFixed(1) : '0.0',
    };
  }

  List<LeadModel> getLeadsByStatus(LeadStatus status) {
    return _leads.where((lead) => lead.status == status).toList();
  }

  List<LeadModel> getOverdueFollowUps() {
    final now = DateTime.now();
    return _leads
        .where((lead) =>
            lead.followUpDate != null && lead.followUpDate!.isBefore(now))
        .toList();
  }

  List<LeadModel> getTodayFollowUps() {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    return _leads
        .where((lead) =>
            lead.followUpDate != null &&
            lead.followUpDate!.isAfter(todayStart) &&
            lead.followUpDate!.isBefore(todayEnd))
        .toList();
  }
}
