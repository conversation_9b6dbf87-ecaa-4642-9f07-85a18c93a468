package com.example.calling_agent_app

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.telephony.TelephonyManager
import android.provider.CallLog
import android.database.Cursor
import io.flutter.plugin.common.MethodChannel
import android.Manifest
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat

class CallEndReceiver(private val methodChannel: MethodChannel?) : BroadcastReceiver() {
    
    private var lastCallState = TelephonyManager.CALL_STATE_IDLE
    private var callStartTime: Long = 0
    private var incomingNumber: String? = null
    
    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) return
        
        val action = intent.action
        if (action == TelephonyManager.ACTION_PHONE_STATE_CHANGED) {
            val state = intent.getStringExtra(TelephonyManager.EXTRA_STATE)
            val phoneNumber = intent.getStringExtra(TelephonyManager.EXTRA_INCOMING_NUMBER)
            
            when (state) {
                TelephonyManager.EXTRA_STATE_RINGING -> {
                    // Incoming call ringing
                    incomingNumber = phoneNumber
                    lastCallState = TelephonyManager.CALL_STATE_RINGING
                }
                TelephonyManager.EXTRA_STATE_OFFHOOK -> {
                    // Call answered or outgoing call started
                    if (lastCallState == TelephonyManager.CALL_STATE_RINGING) {
                        // Incoming call answered
                        callStartTime = System.currentTimeMillis()
                    } else {
                        // Outgoing call started
                        callStartTime = System.currentTimeMillis()
                    }
                    lastCallState = TelephonyManager.CALL_STATE_OFFHOOK
                }
                TelephonyManager.EXTRA_STATE_IDLE -> {
                    // Call ended
                    if (lastCallState == TelephonyManager.CALL_STATE_OFFHOOK) {
                        // Call was active and now ended
                        val callDuration = if (callStartTime > 0) {
                            ((System.currentTimeMillis() - callStartTime) / 1000).toInt()
                        } else {
                            0
                        }
                        
                        // Get the most recent call log entry
                        getLatestCallLog(context) { callLogData ->
                            callLogData?.let {
                                methodChannel?.invokeMethod("onCallEnded", it)
                            }
                        }
                    } else if (lastCallState == TelephonyManager.CALL_STATE_RINGING) {
                        // Missed call
                        getLatestCallLog(context) { callLogData ->
                            callLogData?.let {
                                methodChannel?.invokeMethod("onCallEnded", it)
                            }
                        }
                    }
                    
                    // Reset state
                    lastCallState = TelephonyManager.CALL_STATE_IDLE
                    callStartTime = 0
                    incomingNumber = null
                }
            }
        }
    }
    
    private fun getLatestCallLog(context: Context, callback: (Map<String, Any>?) -> Unit) {
        // Check for permission
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_CALL_LOG) 
            != PackageManager.PERMISSION_GRANTED) {
            callback(null)
            return
        }
        
        try {
            val cursor: Cursor? = context.contentResolver.query(
                CallLog.Calls.CONTENT_URI,
                arrayOf(
                    CallLog.Calls._ID,
                    CallLog.Calls.NUMBER,
                    CallLog.Calls.CACHED_NAME,
                    CallLog.Calls.TYPE,
                    CallLog.Calls.DATE,
                    CallLog.Calls.DURATION
                ),
                null,
                null,
                "${CallLog.Calls.DATE} DESC LIMIT 1"
            )
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val id = it.getString(it.getColumnIndexOrThrow(CallLog.Calls._ID))
                    val number = it.getString(it.getColumnIndexOrThrow(CallLog.Calls.NUMBER)) ?: "Unknown"
                    val name = it.getString(it.getColumnIndexOrThrow(CallLog.Calls.CACHED_NAME))
                    val type = it.getInt(it.getColumnIndexOrThrow(CallLog.Calls.TYPE))
                    val date = it.getLong(it.getColumnIndexOrThrow(CallLog.Calls.DATE))
                    val duration = it.getInt(it.getColumnIndexOrThrow(CallLog.Calls.DURATION))
                    
                    val callType = when (type) {
                        CallLog.Calls.INCOMING_TYPE -> "incoming"
                        CallLog.Calls.OUTGOING_TYPE -> "outgoing"
                        CallLog.Calls.MISSED_TYPE -> "missed"
                        else -> "incoming"
                    }
                    
                    val callData = mapOf(
                        "id" to id,
                        "number" to number,
                        "name" to name,
                        "type" to callType,
                        "timestamp" to date,
                        "duration" to duration
                    )
                    
                    callback(callData)
                } else {
                    callback(null)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            callback(null)
        }
    }
}
