import 'package:flutter/material.dart';
import '../data/models/call_log_model.dart';
import '../data/models/lead_model.dart';
import '../presentation/providers/lead_provider.dart';
import '../presentation/widgets/call_classification_dialog.dart';
import 'call_end_detection_service.dart';
import 'dart:async';

class CallClassificationManager {
  static CallClassificationManager? _instance;
  static CallClassificationManager get instance {
    _instance ??= CallClassificationManager._();
    return _instance!;
  }
  
  CallClassificationManager._();
  
  StreamSubscription<CallLogModel>? _callEndSubscription;
  BuildContext? _context;
  LeadProvider? _leadProvider;
  
  void initialize(BuildContext context, LeadProvider leadProvider) {
    _context = context;
    _leadProvider = leadProvider;
    _startListening();
  }
  
  void _startListening() {
    _callEndSubscription?.cancel();
    _callEndSubscription = CallEndDetectionService.instance.callEndStream.listen(
      _handleCallEnd,
      onError: (error) {
        print('Call end detection error: $error');
      },
    );
    
    CallEndDetectionService.instance.startListening();
  }
  
  void _handleCallEnd(CallLogModel callLog) {
    // Only show popup for answered calls (duration > 0)
    if (callLog.duration > 0 && _context != null && _context!.mounted) {
      _showClassificationDialog(callLog);
    }
  }
  
  void _showClassificationDialog(CallLogModel callLog) {
    if (_context == null || !_context!.mounted) return;
    
    // Check if lead already exists for this number
    final existingLead = _findExistingLead(callLog.number);
    
    showDialog(
      context: _context!,
      barrierDismissible: false, // Force user to make a choice
      builder: (context) => CallClassificationDialog(
        callLog: callLog,
        existingLead: existingLead,
        onClassified: (isBusinessCall) {
          _handleClassification(callLog, isBusinessCall);
        },
        onLeadCreated: (lead) {
          _handleLeadCreated(lead);
        },
        onLeadUpdated: (lead, newStatus) {
          _handleLeadUpdated(lead, newStatus);
        },
      ),
    );
  }
  
  LeadModel? _findExistingLead(String phoneNumber) {
    if (_leadProvider == null) return null;
    
    try {
      return _leadProvider!.leads.firstWhere(
        (lead) => lead.phone == phoneNumber,
      );
    } catch (e) {
      return null; // No existing lead found
    }
  }
  
  void _handleClassification(CallLogModel callLog, bool isBusinessCall) {
    if (isBusinessCall) {
      print('Call classified as business: ${callLog.number}');
      _showSuccessMessage('Call saved as business lead!');
    } else {
      print('Call classified as personal: ${callLog.number}');
    }
  }
  
  void _handleLeadCreated(LeadModel lead) {
    if (_leadProvider != null) {
      _leadProvider!.addLead(lead);
      _showSuccessMessage('New lead created successfully!');
      print('New lead created: ${lead.name} - ${lead.phone}');
    }
  }
  
  void _handleLeadUpdated(LeadModel lead, LeadStatus newStatus) {
    if (_leadProvider != null) {
      _leadProvider!.updateLead(lead);
      _showSuccessMessage('Lead status updated to ${_getStatusDisplayName(newStatus)}!');
      print('Lead updated: ${lead.name} - Status: ${newStatus.name}');
    }
  }
  
  void _showSuccessMessage(String message) {
    if (_context != null && _context!.mounted) {
      ScaffoldMessenger.of(_context!).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }
  
  String _getStatusDisplayName(LeadStatus status) {
    switch (status) {
      case LeadStatus.new_lead:
        return 'New Lead';
      case LeadStatus.contacted:
        return 'Contacted';
      case LeadStatus.qualified:
        return 'Qualified';
      case LeadStatus.proposal:
        return 'Proposal Sent';
      case LeadStatus.negotiation:
        return 'In Negotiation';
      case LeadStatus.closed_won:
        return 'Closed Won';
      case LeadStatus.closed_lost:
        return 'Closed Lost';
      case LeadStatus.follow_up:
        return 'Follow Up';
    }
  }
  
  void dispose() {
    _callEndSubscription?.cancel();
    _callEndSubscription = null;
    CallEndDetectionService.instance.stopListening();
    _context = null;
    _leadProvider = null;
  }
  
  // Manual trigger for testing
  void triggerTestClassification() {
    final testCall = CallLogModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: 'Test Contact',
      number: '+**********',
      type: CallType.outgoing,
      timestamp: DateTime.now(),
      duration: 120, // 2 minutes
      isSynced: false,
      isAnswered: true,
    );
    
    _showClassificationDialog(testCall);
  }
}
