import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/speed_dial_provider.dart';
import '../../providers/contact_provider.dart';
import '../../../data/models/contact_model.dart';

class AddSpeedDialDialog extends StatefulWidget {
  final int position;

  const AddSpeedDialDialog({
    super.key,
    required this.position,
  });

  @override
  State<AddSpeedDialDialog> createState() => _AddSpeedDialDialogState();
}

class _AddSpeedDialDialogState extends State<AddSpeedDialDialog> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<ContactModel> _filteredContacts = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadContacts();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadContacts() {
    final contactProvider = context.read<ContactProvider>();
    _filteredContacts = contactProvider.contacts
        .where((contact) => contact.phoneNumbers.isNotEmpty)
        .toList();
    
    // Sort by frequency and favorites
    _filteredContacts.sort((a, b) {
      if (a.isFavorite && !b.isFavorite) return -1;
      if (!a.isFavorite && b.isFavorite) return 1;
      return b.contactFrequency.compareTo(a.contactFrequency);
    });
    
    setState(() {});
  }

  void _filterContacts(String query) {
    final contactProvider = context.read<ContactProvider>();
    
    if (query.isEmpty) {
      _filteredContacts = contactProvider.contacts
          .where((contact) => contact.phoneNumbers.isNotEmpty)
          .toList();
    } else {
      _filteredContacts = contactProvider.contacts
          .where((contact) {
            final nameMatch = contact.displayName
                .toLowerCase()
                .contains(query.toLowerCase());
            final phoneMatch = contact.phoneNumbers.any((phone) =>
                phone.number.replaceAll(RegExp(r'[^\d]'), '').contains(query));
            return (nameMatch || phoneMatch) && contact.phoneNumbers.isNotEmpty;
          })
          .toList();
    }
    
    // Sort filtered results
    _filteredContacts.sort((a, b) {
      if (a.isFavorite && !b.isFavorite) return -1;
      if (!a.isFavorite && b.isFavorite) return 1;
      return b.contactFrequency.compareTo(a.contactFrequency);
    });
    
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Text(
                  'Add to Speed Dial ${widget.position}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Search bar
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search contacts...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                          _filterContacts('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _filterContacts(value);
              },
            ),
            const SizedBox(height: 16),

            // Contact list
            Expanded(
              child: _filteredContacts.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.contacts_outlined,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isEmpty
                                ? 'No contacts with phone numbers'
                                : 'No contacts match your search',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _filteredContacts.length,
                      itemBuilder: (context, index) {
                        final contact = _filteredContacts[index];
                        return _buildContactItem(contact);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem(ContactModel contact) {
    final speedDialProvider = context.read<SpeedDialProvider>();
    final isAlreadyInSpeedDial = speedDialProvider.isContactInSpeedDial(contact.id);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: isAlreadyInSpeedDial
            ? Border.all(color: Colors.orange, width: 1)
            : null,
      ),
      child: ListTile(
        leading: _buildContactAvatar(contact),
        title: Row(
          children: [
            Expanded(
              child: Text(
                contact.displayName,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (contact.isFavorite)
              Icon(
                Icons.star,
                size: 16,
                color: Colors.amber.shade600,
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (contact.phoneNumbers.isNotEmpty)
              Text(contact.phoneNumbers.first.number),
            if (isAlreadyInSpeedDial) ...[
              const SizedBox(height: 4),
              Text(
                'Already in speed dial ${speedDialProvider.findContactPosition(contact.id)}',
                style: TextStyle(
                  color: Colors.orange.shade700,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
        trailing: isAlreadyInSpeedDial
            ? Icon(
                Icons.warning,
                color: Colors.orange.shade600,
                size: 20,
              )
            : const Icon(Icons.add),
        onTap: isAlreadyInSpeedDial
            ? () => _showAlreadyInSpeedDialDialog(contact)
            : () => _addToSpeedDial(contact),
      ),
    );
  }

  Widget _buildContactAvatar(ContactModel contact) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: _getAvatarColors(contact.displayName),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: contact.avatar != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.memory(
                contact.avatar!,
                width: 40,
                height: 40,
                fit: BoxFit.cover,
              ),
            )
          : Center(
              child: Text(
                _getInitials(contact.displayName),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
    );
  }

  void _addToSpeedDial(ContactModel contact) async {
    try {
      final speedDialProvider = context.read<SpeedDialProvider>();
      await speedDialProvider.setSpeedDialContact(contact, widget.position);
      
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${contact.displayName} added to speed dial ${widget.position}'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding contact: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAlreadyInSpeedDialDialog(ContactModel contact) {
    final speedDialProvider = context.read<SpeedDialProvider>();
    final currentPosition = speedDialProvider.findContactPosition(contact.id);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Already in Speed Dial'),
        content: Text(
          '${contact.displayName} is already in speed dial position $currentPosition. '
          'Do you want to move it to position ${widget.position}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await speedDialProvider.moveContact(currentPosition!, widget.position);
                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        '${contact.displayName} moved to speed dial ${widget.position}',
                      ),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error moving contact: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Move'),
          ),
        ],
      ),
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  List<Color> _getAvatarColors(String name) {
    final hash = name.hashCode;
    final colors = [
      [Colors.blue.shade400, Colors.blue.shade600],
      [Colors.green.shade400, Colors.green.shade600],
      [Colors.purple.shade400, Colors.purple.shade600],
      [Colors.orange.shade400, Colors.orange.shade600],
      [Colors.teal.shade400, Colors.teal.shade600],
      [Colors.pink.shade400, Colors.pink.shade600],
      [Colors.indigo.shade400, Colors.indigo.shade600],
      [Colors.red.shade400, Colors.red.shade600],
    ];
    
    return colors[hash.abs() % colors.length];
  }
}
