// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LeadModelAdapter extends TypeAdapter<LeadModel> {
  @override
  final int typeId = 1;

  @override
  LeadModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LeadModel(
      id: fields[0] as String,
      name: fields[1] as String,
      phone: fields[2] as String,
      email: fields[3] as String?,
      status: fields[4] as LeadStatus,
      notes: fields[5] as String?,
      followUpDate: fields[6] as DateTime?,
      createdAt: fields[7] as DateTime,
      updatedAt: fields[8] as DateTime,
      source: fields[9] as String?,
      estimatedValue: fields[10] as double?,
      isSynced: fields[11] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, LeadModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.phone)
      ..writeByte(3)
      ..write(obj.email)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.notes)
      ..writeByte(6)
      ..write(obj.followUpDate)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt)
      ..writeByte(9)
      ..write(obj.source)
      ..writeByte(10)
      ..write(obj.estimatedValue)
      ..writeByte(11)
      ..write(obj.isSynced);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LeadModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LeadStatusAdapter extends TypeAdapter<LeadStatus> {
  @override
  final int typeId = 2;

  @override
  LeadStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return LeadStatus.new_lead;
      case 1:
        return LeadStatus.contacted;
      case 2:
        return LeadStatus.qualified;
      case 3:
        return LeadStatus.proposal;
      case 4:
        return LeadStatus.negotiation;
      case 5:
        return LeadStatus.closed_won;
      case 6:
        return LeadStatus.closed_lost;
      case 7:
        return LeadStatus.follow_up;
      default:
        return LeadStatus.new_lead;
    }
  }

  @override
  void write(BinaryWriter writer, LeadStatus obj) {
    switch (obj) {
      case LeadStatus.new_lead:
        writer.writeByte(0);
        break;
      case LeadStatus.contacted:
        writer.writeByte(1);
        break;
      case LeadStatus.qualified:
        writer.writeByte(2);
        break;
      case LeadStatus.proposal:
        writer.writeByte(3);
        break;
      case LeadStatus.negotiation:
        writer.writeByte(4);
        break;
      case LeadStatus.closed_won:
        writer.writeByte(5);
        break;
      case LeadStatus.closed_lost:
        writer.writeByte(6);
        break;
      case LeadStatus.follow_up:
        writer.writeByte(7);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LeadStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
