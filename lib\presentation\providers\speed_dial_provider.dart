import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/models/contact_model.dart';
import '../../data/services/local_storage_service.dart';

class SpeedDialProvider with ChangeNotifier {
  final LocalStorageService _localStorage = LocalStorageService();
  
  // Speed dial supports positions 1-9 (like phone keypad)
  final Map<int, ContactModel?> _speedDialContacts = {
    for (int i = 1; i <= 9; i++) i: null,
  };
  
  bool _isEditMode = false;

  Map<int, ContactModel?> get speedDialContacts => _speedDialContacts;
  bool get isEditMode => _isEditMode;

  SpeedDialProvider() {
    loadSpeedDialContacts();
  }

  Future<void> loadSpeedDialContacts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      for (int position = 1; position <= 9; position++) {
        final contactId = prefs.getString('speed_dial_$position');
        if (contactId != null) {
          // Load contact from local storage
          final contacts = await _localStorage.getContacts();
          final contact = contacts.firstWhere(
            (c) => c.id == contactId,
            orElse: () => ContactModel(
              id: '',
              displayName: '',
              phoneNumbers: [],
              emailAddresses: [],
            ),
          );
          
          if (contact.id.isNotEmpty) {
            _speedDialContacts[position] = contact;
          }
        }
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading speed dial contacts: $e');
    }
  }

  Future<void> setSpeedDialContact(ContactModel contact, int position) async {
    try {
      if (position < 1 || position > 9) {
        throw ArgumentError('Speed dial position must be between 1 and 9');
      }

      _speedDialContacts[position] = contact;
      
      // Save to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('speed_dial_$position', contact.id);
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting speed dial contact: $e');
      rethrow;
    }
  }

  Future<void> removeSpeedDialContact(int position) async {
    try {
      if (position < 1 || position > 9) {
        throw ArgumentError('Speed dial position must be between 1 and 9');
      }

      _speedDialContacts[position] = null;
      
      // Remove from preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('speed_dial_$position');
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error removing speed dial contact: $e');
      rethrow;
    }
  }

  ContactModel? getSpeedDialContact(int position) {
    if (position < 1 || position > 9) return null;
    return _speedDialContacts[position];
  }

  bool isPositionOccupied(int position) {
    return _speedDialContacts[position] != null;
  }

  List<int> getAvailablePositions() {
    return _speedDialContacts.entries
        .where((entry) => entry.value == null)
        .map((entry) => entry.key)
        .toList();
  }

  List<int> getOccupiedPositions() {
    return _speedDialContacts.entries
        .where((entry) => entry.value != null)
        .map((entry) => entry.key)
        .toList();
  }

  int? findContactPosition(String contactId) {
    for (final entry in _speedDialContacts.entries) {
      if (entry.value?.id == contactId) {
        return entry.key;
      }
    }
    return null;
  }

  bool isContactInSpeedDial(String contactId) {
    return findContactPosition(contactId) != null;
  }

  Future<void> moveContact(int fromPosition, int toPosition) async {
    try {
      if (fromPosition < 1 || fromPosition > 9 || toPosition < 1 || toPosition > 9) {
        throw ArgumentError('Speed dial positions must be between 1 and 9');
      }

      final contact = _speedDialContacts[fromPosition];
      if (contact == null) {
        throw StateError('No contact at position $fromPosition');
      }

      // Remove from old position
      await removeSpeedDialContact(fromPosition);
      
      // Add to new position
      await setSpeedDialContact(contact, toPosition);
    } catch (e) {
      debugPrint('Error moving speed dial contact: $e');
      rethrow;
    }
  }

  Future<void> swapContacts(int position1, int position2) async {
    try {
      if (position1 < 1 || position1 > 9 || position2 < 1 || position2 > 9) {
        throw ArgumentError('Speed dial positions must be between 1 and 9');
      }

      final contact1 = _speedDialContacts[position1];
      final contact2 = _speedDialContacts[position2];

      // Temporarily remove both
      _speedDialContacts[position1] = null;
      _speedDialContacts[position2] = null;

      final prefs = await SharedPreferences.getInstance();
      
      // Set new positions
      if (contact1 != null) {
        _speedDialContacts[position2] = contact1;
        await prefs.setString('speed_dial_$position2', contact1.id);
      } else {
        await prefs.remove('speed_dial_$position2');
      }

      if (contact2 != null) {
        _speedDialContacts[position1] = contact2;
        await prefs.setString('speed_dial_$position1', contact2.id);
      } else {
        await prefs.remove('speed_dial_$position1');
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error swapping speed dial contacts: $e');
      rethrow;
    }
  }

  Future<void> clearAllSpeedDial() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      for (int position = 1; position <= 9; position++) {
        _speedDialContacts[position] = null;
        await prefs.remove('speed_dial_$position');
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing all speed dial: $e');
      rethrow;
    }
  }

  void toggleEditMode() {
    _isEditMode = !_isEditMode;
    notifyListeners();
  }

  void setEditMode(bool editMode) {
    _isEditMode = editMode;
    notifyListeners();
  }

  // Quick dial functionality
  Future<ContactModel?> quickDial(int digit) async {
    if (digit < 1 || digit > 9) return null;
    
    final contact = _speedDialContacts[digit];
    if (contact != null && contact.phoneNumbers.isNotEmpty) {
      // Update contact frequency
      try {
        final updatedContact = contact.copyWith(
          contactFrequency: contact.contactFrequency + 1,
          lastContactedDate: DateTime.now(),
        );
        await _localStorage.saveContact(updatedContact);
        _speedDialContacts[digit] = updatedContact;
        notifyListeners();
      } catch (e) {
        debugPrint('Error updating contact frequency: $e');
      }
      
      return contact;
    }
    
    return null;
  }

  // Statistics
  Map<String, dynamic> getSpeedDialStats() {
    final occupiedCount = getOccupiedPositions().length;
    final availableCount = getAvailablePositions().length;
    
    return {
      'total_slots': 9,
      'occupied_slots': occupiedCount,
      'available_slots': availableCount,
      'usage_percentage': (occupiedCount / 9 * 100).round(),
    };
  }

  List<ContactModel> getMostUsedSpeedDialContacts() {
    final contacts = _speedDialContacts.values
        .where((contact) => contact != null)
        .cast<ContactModel>()
        .toList();
    
    contacts.sort((a, b) => b.contactFrequency.compareTo(a.contactFrequency));
    return contacts;
  }

  // Backup and restore
  Map<String, dynamic> exportSpeedDialData() {
    final data = <String, dynamic>{};
    
    for (final entry in _speedDialContacts.entries) {
      if (entry.value != null) {
        data[entry.key.toString()] = entry.value!.toJson();
      }
    }
    
    return data;
  }

  Future<void> importSpeedDialData(Map<String, dynamic> data) async {
    try {
      // Clear existing speed dial
      await clearAllSpeedDial();
      
      // Import new data
      for (final entry in data.entries) {
        final position = int.tryParse(entry.key);
        if (position != null && position >= 1 && position <= 9) {
          final contactData = entry.value as Map<String, dynamic>;
          final contact = ContactModel.fromJson(contactData);
          await setSpeedDialContact(contact, position);
        }
      }
    } catch (e) {
      debugPrint('Error importing speed dial data: $e');
      rethrow;
    }
  }

  // Auto-assign functionality
  Future<void> autoAssignFavoriteContacts() async {
    try {
      final contacts = await _localStorage.getContacts();
      final favoriteContacts = contacts
          .where((contact) => contact.isFavorite && contact.phoneNumbers.isNotEmpty)
          .toList();
      
      // Sort by contact frequency
      favoriteContacts.sort((a, b) => b.contactFrequency.compareTo(a.contactFrequency));
      
      // Assign to available positions
      final availablePositions = getAvailablePositions();
      
      for (int i = 0; i < favoriteContacts.length && i < availablePositions.length; i++) {
        await setSpeedDialContact(favoriteContacts[i], availablePositions[i]);
      }
    } catch (e) {
      debugPrint('Error auto-assigning favorite contacts: $e');
      rethrow;
    }
  }
}
