import 'package:flutter/material.dart';
import 'package:calling_agent_app/data/models/call_log_model.dart';

class Gamification<PERSON>rovider with ChangeNotifier {
  List<Badge> _badges = [];

  List<Badge> get badges => _badges;

  void checkForNewBadges(List<CallLogModel> callLogs) {
    // Longest Call Badge
    if (callLogs.any((log) => log.duration > 3600)) {
      _addBadge(
        Badge(
          name: 'Marathon Caller',
          description: 'Made a call longer than 1 hour',
          icon: Icons.timer,
        ),
      );
    }

    // 100 Calls Badge
    if (callLogs.length >= 100) {
      _addBadge(
        Badge(
          name: '<PERSON> Caller',
          description: 'Made 100 calls',
          icon: Icons.star,
        ),
      );
    }

    // Perfect Follow-up Badge
    // TODO: Implement logic for perfect follow-up

    notifyListeners();
  }

  void _addBadge(Badge newBadge) {
    if (!_badges.any((badge) => badge.name == newBadge.name)) {
      _badges.add(newBadge);
    }
  }
}

class Badge {
  final String name;
  final String description;
  final IconData icon;

  Badge({
    required this.name,
    required this.description,
    required this.icon,
  });
}
