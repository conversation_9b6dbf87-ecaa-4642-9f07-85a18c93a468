import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_phone_direct_caller/flutter_phone_direct_caller.dart';
import 'package:vibration/vibration.dart';
import '../../providers/dialer_provider.dart';
import '../../widgets/dialer/number_pad.dart';
import '../../widgets/dialer/call_controls.dart';

import '../speed_dial/speed_dial_screen.dart';
import '../contacts/contacts_screen.dart';
import '../call_logs/call_logs_screen.dart';

class DialerScreen extends StatefulWidget {
  const DialerScreen({super.key});

  @override
  State<DialerScreen> createState() => _DialerScreenState();
}

class _DialerScreenState extends State<DialerScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text(
          'Dialer',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.contacts),
            onPressed: () => _navigateToContacts(context),
            tooltip: 'Contacts',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => _navigateToCallHistory(context),
            tooltip: 'Call History',
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () => _showDialerOptions(context),
          ),
        ],
      ),
      body: Consumer<DialerProvider>(
        builder: (context, dialerProvider, child) {
          return Column(
            children: [
              // Phone Number Display
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Main number display
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 15,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        dialerProvider.phoneNumber.isEmpty
                            ? 'Enter phone number'
                            : dialerProvider.formattedPhoneNumber,
                        style: TextStyle(
                          fontSize:
                              dialerProvider.phoneNumber.isEmpty ? 16 : 24,
                          fontWeight: FontWeight.w500,
                          color: dialerProvider.phoneNumber.isEmpty
                              ? Colors.grey
                              : Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 10),
                    // Contact name if found
                    if (dialerProvider.matchedContact != null)
                      Text(
                        dialerProvider.matchedContact!.displayName,
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ),

              // Flexible content area
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Number Pad
                    const NumberPad(),

                    const SizedBox(height: 20),

                    // Call Controls
                    CallControls(
                      onCall: () =>
                          _makeCall(context, dialerProvider.phoneNumber),
                      onDelete: () => dialerProvider.deleteDigit(),
                      onLongPressDelete: () => dialerProvider.clearNumber(),
                      hasNumber: dialerProvider.phoneNumber.isNotEmpty,
                      pulseAnimation: _pulseAnimation,
                    ),

                    const SizedBox(height: 30),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _makeCall(BuildContext context, String phoneNumber) async {
    if (phoneNumber.isEmpty) return;

    // Get provider reference before async operations
    final dialerProvider = context.read<DialerProvider>();

    try {
      // Add haptic feedback
      final hasVibrator = await Vibration.hasVibrator();
      if (hasVibrator == true) {
        Vibration.vibrate(duration: 100);
      }

      // Start pulse animation
      _pulseController.repeat(reverse: true);

      // Log the call first
      await dialerProvider.logCall(phoneNumber);

      // Make the call
      await FlutterPhoneDirectCaller.callNumber(phoneNumber);

      // Stop animation after a delay
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          _pulseController.stop();
          _pulseController.reset();
        }
      });
    } catch (e) {
      // Stop animation on error
      _pulseController.stop();
      _pulseController.reset();

      // Show error dialog
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showErrorDialog(context, 'Failed to make call: $e');
        });
      }
    }
  }

  void _navigateToContacts(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ContactsScreen(),
      ),
    );
  }

  void _navigateToCallHistory(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CallLogsScreen(),
      ),
    );
  }

  void _showDialerOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.star),
              title: const Text('Speed Dial'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SpeedDialScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('Call History'),
              onTap: () {
                Navigator.pop(context);
                _navigateToCallHistory(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
