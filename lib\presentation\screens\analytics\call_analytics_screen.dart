import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../providers/call_log_provider.dart';
import '../../../data/models/call_log_model.dart';

class CallAnalyticsScreen extends StatefulWidget {
  const CallAnalyticsScreen({super.key});

  @override
  State<CallAnalyticsScreen> createState() => _CallAnalyticsScreenState();
}

class _CallAnalyticsScreenState extends State<CallAnalyticsScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  String _selectedPeriod = 'This Week';
  final List<String> _periods = [
    'Today',
    'This Week',
    'This Month',
    'Last 30 Days',
    'Last 90 Days'
  ];

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<CallLogProvider>(
          builder: (context, provider, child) {
            final filteredLogs = _getFilteredLogs(provider.callLogs);
            final analytics = _calculateAnalytics(filteredLogs);

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Period Selector
                  _buildPeriodSelector(),

                  const SizedBox(height: 24),

                  // Advanced Analytics Cards (Unique insights)
                  _buildAdvancedAnalyticsCards(analytics, filteredLogs),

                  const SizedBox(height: 24),

                  // Enhanced Multi-Series Activity Chart
                  _buildEnhancedActivityChart(filteredLogs),

                  const SizedBox(height: 24),

                  // Call Success Rate Trends
                  _buildSuccessRateTrends(filteredLogs),

                  const SizedBox(height: 24),

                  // Fixed Peak Hours Chart with better labels
                  _buildImprovedPeakHoursChart(filteredLogs),

                  const SizedBox(height: 24),

                  // Call Volume Heatmap
                  _buildCallVolumeHeatmap(filteredLogs),

                  const SizedBox(height: 24),

                  // Duration by Time Analysis
                  _buildDurationByTimeAnalysis(filteredLogs),

                  const SizedBox(height: 24),

                  // Comparative Analytics
                  _buildComparativeAnalytics(provider.callLogs),

                  const SizedBox(height: 24),

                  // Enhanced Top Contacts with Engagement Patterns
                  _buildEnhancedTopContacts(filteredLogs),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Call Analytics',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.share),
          onPressed: _shareAnalytics,
          tooltip: 'Share Analytics',
        ),
      ],
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _periods.length,
        itemBuilder: (context, index) {
          final period = _periods[index];
          final isSelected = period == _selectedPeriod;

          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: FilterChip(
              label: Text(period),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedPeriod = period;
                });
              },
              backgroundColor: Colors.white,
              selectedColor:
                  Theme.of(context).primaryColor.withValues(alpha: 0.1),
              checkmarkColor: Theme.of(context).primaryColor,
              labelStyle: TextStyle(
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade700,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              side: BorderSide(
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade300,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildOverviewCards(Map<String, dynamic> analytics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.5,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildMetricCard(
          'Total Calls',
          '${analytics['totalCalls']}',
          Icons.call,
          Colors.blue,
          subtitle: '${analytics['totalDuration']} total',
        ),
        _buildMetricCard(
          'Answered Rate',
          '${analytics['answerRate']}%',
          Icons.call_received,
          Colors.green,
          subtitle: '${analytics['answeredCalls']} answered',
        ),
        _buildMetricCard(
          'Avg Duration',
          '${analytics['avgDuration']}',
          Icons.timer,
          Colors.orange,
          subtitle: 'Per answered call',
        ),
        _buildMetricCard(
          'Missed Calls',
          '${analytics['missedCalls']}',
          Icons.call_missed,
          Colors.red,
          subtitle: '${analytics['missedRate']}% of total',
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCallTypeChart(Map<String, dynamic> analytics) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Call Distribution',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: _buildPieChartSections(analytics),
                centerSpaceRadius: 60,
                sectionsSpace: 2,
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildChartLegend(analytics),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(
      Map<String, dynamic> analytics) {
    final total = analytics['totalCalls'] as int;
    if (total == 0) return [];

    return [
      PieChartSectionData(
        value: (analytics['outgoingCalls'] as int).toDouble(),
        color: Colors.green,
        title:
            '${((analytics['outgoingCalls'] as int) / total * 100).round()}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        value: (analytics['incomingCalls'] as int).toDouble(),
        color: Colors.blue,
        title:
            '${((analytics['incomingCalls'] as int) / total * 100).round()}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        value: (analytics['missedCalls'] as int).toDouble(),
        color: Colors.red,
        title: '${((analytics['missedCalls'] as int) / total * 100).round()}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    ];
  }

  Widget _buildChartLegend(Map<String, dynamic> analytics) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildLegendItem('Outgoing', Colors.green, analytics['outgoingCalls']),
        _buildLegendItem('Incoming', Colors.blue, analytics['incomingCalls']),
        _buildLegendItem('Missed', Colors.red, analytics['missedCalls']),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color, int count) {
    return Column(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        Text(
          '$count',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  // Helper methods for data processing and calculations
  List<CallLogModel> _getFilteredLogs(List<CallLogModel> allLogs) {
    final now = DateTime.now();
    DateTime startDate;

    switch (_selectedPeriod) {
      case 'Today':
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case 'This Week':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        break;
      case 'This Month':
        startDate = DateTime(now.year, now.month, 1);
        break;
      case 'Last 30 Days':
        startDate = now.subtract(const Duration(days: 30));
        break;
      case 'Last 90 Days':
        startDate = now.subtract(const Duration(days: 90));
        break;
      default:
        startDate = now.subtract(const Duration(days: 7));
    }

    return allLogs.where((log) => log.timestamp.isAfter(startDate)).toList();
  }

  Map<String, dynamic> _calculateAnalytics(List<CallLogModel> logs) {
    final totalCalls = logs.length;
    final outgoingCalls =
        logs.where((log) => log.type == CallType.outgoing).length;
    final incomingCalls =
        logs.where((log) => log.type == CallType.incoming).length;
    final missedCalls = logs.where((log) => log.type == CallType.missed).length;

    final answeredCalls = logs.where((log) => log.duration > 0).length;
    final totalDuration = logs.fold<int>(0, (sum, log) => sum + log.duration);

    final quickCalls = logs.where((log) => log.duration < 30).length;
    final shortCalls =
        logs.where((log) => log.duration >= 30 && log.duration < 120).length;
    final mediumCalls =
        logs.where((log) => log.duration >= 120 && log.duration < 600).length;
    final longCalls = logs.where((log) => log.duration >= 600).length;

    return {
      'totalCalls': totalCalls,
      'outgoingCalls': outgoingCalls,
      'incomingCalls': incomingCalls,
      'missedCalls': missedCalls,
      'answeredCalls': answeredCalls,
      'answerRate':
          totalCalls > 0 ? ((answeredCalls / totalCalls) * 100).round() : 0,
      'missedRate':
          totalCalls > 0 ? ((missedCalls / totalCalls) * 100).round() : 0,
      'totalDuration': _formatDuration(totalDuration),
      'avgDuration': answeredCalls > 0
          ? _formatDuration(totalDuration ~/ answeredCalls)
          : '0s',
      'quickCalls': quickCalls,
      'shortCalls': shortCalls,
      'mediumCalls': mediumCalls,
      'longCalls': longCalls,
    };
  }

  String _formatDuration(int seconds) {
    if (seconds < 60) {
      return '${seconds}s';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return remainingSeconds > 0
          ? '${minutes}m ${remainingSeconds}s'
          : '${minutes}m';
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return minutes > 0 ? '${hours}h ${minutes}m' : '${hours}h';
    }
  }

  Widget _buildDailyActivityChart(List<CallLogModel> logs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Daily Activity (Last 7 Days)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(show: false),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: true, reservedSize: 40),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: true, reservedSize: 30),
                  ),
                  rightTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  LineChartBarData(
                    spots: _getDailyActivitySpots(logs),
                    isCurved: true,
                    color: Theme.of(context).primaryColor,
                    barWidth: 3,
                    dotData: FlDotData(show: true),
                    belowBarData: BarAreaData(
                      show: true,
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDurationAnalysis(Map<String, dynamic> analytics) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Call Duration Analysis',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          _buildDurationBar(
              'Quick (<30s)', analytics['quickCalls'], Colors.red),
          _buildDurationBar(
              'Short (30s-2m)', analytics['shortCalls'], Colors.orange),
          _buildDurationBar(
              'Medium (2m-10m)', analytics['mediumCalls'], Colors.blue),
          _buildDurationBar(
              'Long (>10m)', analytics['longCalls'], Colors.green),
        ],
      ),
    );
  }

  Widget _buildDurationBar(String label, int count, Color color) {
    final total = context.read<CallLogProvider>().callLogs.length;
    final percentage = total > 0 ? (count / total * 100) : 0.0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$count (${percentage.toStringAsFixed(1)}%)',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildPeakHoursChart(List<CallLogModel> logs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Peak Hours',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                gridData: FlGridData(show: false),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: true, reservedSize: 40),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      getTitlesWidget: (value, meta) {
                        return Text('${value.toInt()}h');
                      },
                    ),
                  ),
                  rightTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false),
                barGroups: _getHourlyBarGroups(logs),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopContacts(List<CallLogModel> logs) {
    if (logs.isEmpty) {
      return _buildEmptyTopContacts();
    }

    final contactCounts = <String, Map<String, dynamic>>{};

    for (final log in logs) {
      final key = log.name ?? log.number;
      if (key.isEmpty) continue; // Skip empty keys

      if (contactCounts.containsKey(key)) {
        contactCounts[key]!['count']++;
        contactCounts[key]!['duration'] += log.duration;
      } else {
        contactCounts[key] = {
          'count': 1,
          'duration': log.duration,
          'name': log.name ?? log.number,
          'number': log.number,
        };
      }
    }

    if (contactCounts.isEmpty) {
      return _buildEmptyTopContacts();
    }

    final sortedContacts = contactCounts.entries.toList()
      ..sort((a, b) => b.value['count'].compareTo(a.value['count']));

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Top Contacts',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 16),
          ...sortedContacts.take(5).map((entry) {
            final contact = entry.value;
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundColor:
                        Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    child: Text(
                      _getContactInitial(contact['name']),
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          contact['name'],
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          '${contact['count']} calls • ${_formatDuration(contact['duration'])}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  List<FlSpot> _getDailyActivitySpots(List<CallLogModel> logs) {
    final dailyCounts = <int, int>{};
    final now = DateTime.now();

    // Initialize with last 7 days
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayKey = date.day;
      dailyCounts[dayKey] = 0;
    }

    // Count calls for each day
    for (final log in logs) {
      final dayKey = log.timestamp.day;
      if (dailyCounts.containsKey(dayKey)) {
        dailyCounts[dayKey] = dailyCounts[dayKey]! + 1;
      }
    }

    // Convert to FlSpot list with proper indexing
    final spots = <FlSpot>[];
    int index = 0;
    for (final entry in dailyCounts.entries) {
      spots.add(FlSpot(index.toDouble(), entry.value.toDouble()));
      index++;
    }

    // Ensure we always have at least 2 points for the chart
    if (spots.length < 2) {
      return [
        const FlSpot(0, 0),
        const FlSpot(1, 0),
      ];
    }

    return spots;
  }

  List<BarChartGroupData> _getHourlyBarGroups(List<CallLogModel> logs) {
    final hourlyCounts = <int, int>{};

    // Initialize all 24 hours with 0
    for (int hour = 0; hour < 24; hour++) {
      hourlyCounts[hour] = 0;
    }

    // Count calls for each hour
    for (final log in logs) {
      final hour = log.timestamp.hour;
      if (hour >= 0 && hour < 24) {
        hourlyCounts[hour] = hourlyCounts[hour]! + 1;
      }
    }

    // Convert to bar chart data
    return hourlyCounts.entries
        .map((entry) => BarChartGroupData(
              x: entry.key,
              barRods: [
                BarChartRodData(
                  toY: entry.value.toDouble(),
                  color: Theme.of(context).primaryColor,
                  width: 16,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ))
        .toList();
  }

  Widget _buildEmptyTopContacts() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Top Contacts',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          Icon(
            Icons.contacts_outlined,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 12),
          Text(
            'No contact data available',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Make some calls to see your top contacts',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  String _getContactInitial(dynamic name) {
    if (name == null) return '?';
    final nameStr = name.toString().trim();
    if (nameStr.isEmpty) return '?';
    return nameStr.substring(0, 1).toUpperCase();
  }

  // Advanced Analytics Methods
  Widget _buildAdvancedAnalyticsCards(
      Map<String, dynamic> analytics, List<CallLogModel> logs) {
    final advancedMetrics = _calculateAdvancedMetrics(logs);

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.1, // Reduced from 1.3 to 1.1 for more height
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildAdvancedMetricCard(
          'Peak Productivity Hour',
          advancedMetrics['peakHour'],
          Icons.schedule,
          Colors.purple,
          subtitle: '${advancedMetrics['peakHourCalls']} calls',
        ),
        _buildAdvancedMetricCard(
          'Call Efficiency',
          '${advancedMetrics['efficiency']}%',
          Icons.trending_up,
          Colors.green,
          subtitle: 'Success rate trend',
        ),
        _buildAdvancedMetricCard(
          'Avg Response Time',
          advancedMetrics['avgResponseTime'],
          Icons.timer,
          Colors.blue,
          subtitle: 'For incoming calls',
        ),
        _buildAdvancedMetricCard(
          'Best Contact Day',
          advancedMetrics['bestDay'],
          Icons.calendar_today,
          Colors.orange,
          subtitle: '${advancedMetrics['bestDayCalls']} calls',
        ),
      ],
    );
  }

  Widget _buildAdvancedMetricCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // Prevent overflow
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6), // Reduced padding
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 18), // Reduced icon size
              ),
              const Spacer(),
              Icon(
                Icons.insights,
                color: color.withValues(alpha: 0.6),
                size: 14, // Reduced icon size
              ),
            ],
          ),
          const SizedBox(height: 8), // Reduced spacing
          Flexible(
            // Allow text to shrink if needed
            child: Text(
              value,
              style: TextStyle(
                fontSize: 18, // Reduced font size
                fontWeight: FontWeight.bold,
                color: color,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 2), // Reduced spacing
          Flexible(
            // Allow text to shrink if needed
            child: Text(
              title,
              style: TextStyle(
                fontSize: 11, // Reduced font size
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2), // Reduced spacing
            Flexible(
              // Allow text to shrink if needed
              child: Text(
                subtitle,
                style: TextStyle(
                  fontSize: 9, // Reduced font size
                  color: Colors.grey.shade500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Map<String, dynamic> _calculateAdvancedMetrics(List<CallLogModel> logs) {
    if (logs.isEmpty) {
      return {
        'peakHour': '--',
        'peakHourCalls': 0,
        'efficiency': 0,
        'avgResponseTime': '--',
        'bestDay': '--',
        'bestDayCalls': 0,
      };
    }

    // Calculate peak hour
    final hourCounts = <int, int>{};
    for (int i = 0; i < 24; i++) {
      hourCounts[i] = 0;
    }

    for (final log in logs) {
      hourCounts[log.timestamp.hour] = hourCounts[log.timestamp.hour]! + 1;
    }

    final peakHour =
        hourCounts.entries.reduce((a, b) => a.value > b.value ? a : b);

    // Calculate efficiency (answered calls / total calls)
    final totalCalls = logs.length;
    final answeredCalls = logs.where((log) => log.duration > 0).length;
    final efficiency =
        totalCalls > 0 ? ((answeredCalls / totalCalls) * 100).round() : 0;

    // Calculate best day of week
    final dayCounts = <int, int>{};
    for (int i = 1; i <= 7; i++) {
      dayCounts[i] = 0;
    }

    for (final log in logs) {
      dayCounts[log.timestamp.weekday] = dayCounts[log.timestamp.weekday]! + 1;
    }

    final bestDayEntry =
        dayCounts.entries.reduce((a, b) => a.value > b.value ? a : b);
    final dayNames = ['', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    return {
      'peakHour': _formatHour(peakHour.key),
      'peakHourCalls': peakHour.value,
      'efficiency': efficiency,
      'avgResponseTime': '< 30s', // Placeholder - would need call timing data
      'bestDay': dayNames[bestDayEntry.key],
      'bestDayCalls': bestDayEntry.value,
    };
  }

  String _formatHour(int hour) {
    if (hour == 0) return '12 AM';
    if (hour < 12) return '$hour AM';
    if (hour == 12) return '12 PM';
    return '${hour - 12} PM';
  }

  Widget _buildEnhancedActivityChart(List<CallLogModel> logs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Call Activity Trends',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Last 7 Days',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 250,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: 2,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.shade200,
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          value.toInt().toString(),
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        );
                      },
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      getTitlesWidget: (value, meta) {
                        final days = [
                          'Mon',
                          'Tue',
                          'Wed',
                          'Thu',
                          'Fri',
                          'Sat',
                          'Sun'
                        ];
                        final index = value.toInt();
                        if (index >= 0 && index < days.length) {
                          return Text(
                            days[index],
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  rightTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false),
                lineBarsData: _getMultiSeriesData(logs),
                lineTouchData: LineTouchData(
                  enabled: true,
                  touchTooltipData: LineTouchTooltipData(
                    getTooltipItems: (touchedSpots) {
                      return touchedSpots.map((spot) {
                        final callTypes = ['Incoming', 'Outgoing', 'Missed'];
                        final colors = [Colors.green, Colors.blue, Colors.red];
                        return LineTooltipItem(
                          '${callTypes[spot.barIndex]}: ${spot.y.toInt()} calls',
                          TextStyle(
                            color: colors[spot.barIndex],
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildChartLegendMultiSeries(),
        ],
      ),
    );
  }

  List<LineChartBarData> _getMultiSeriesData(List<CallLogModel> logs) {
    final dailyData = _calculateDailyMultiSeriesData(logs);

    return [
      // Incoming calls line
      LineChartBarData(
        spots: dailyData['incoming']!,
        isCurved: true,
        color: Colors.green,
        barWidth: 3,
        dotData: FlDotData(show: true),
        belowBarData: BarAreaData(show: false),
      ),
      // Outgoing calls line
      LineChartBarData(
        spots: dailyData['outgoing']!,
        isCurved: true,
        color: Colors.blue,
        barWidth: 3,
        dotData: FlDotData(show: true),
        belowBarData: BarAreaData(show: false),
      ),
      // Missed calls line
      LineChartBarData(
        spots: dailyData['missed']!,
        isCurved: true,
        color: Colors.red,
        barWidth: 3,
        dotData: FlDotData(show: true),
        belowBarData: BarAreaData(show: false),
      ),
    ];
  }

  Map<String, List<FlSpot>> _calculateDailyMultiSeriesData(
      List<CallLogModel> logs) {
    final now = DateTime.now();
    final incomingData = <FlSpot>[];
    final outgoingData = <FlSpot>[];
    final missedData = <FlSpot>[];

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final dayLogs = logs
          .where((log) =>
              log.timestamp.isAfter(dayStart) && log.timestamp.isBefore(dayEnd))
          .toList();

      final incoming =
          dayLogs.where((log) => log.type == CallType.incoming).length;
      final outgoing =
          dayLogs.where((log) => log.type == CallType.outgoing).length;
      final missed = dayLogs.where((log) => log.type == CallType.missed).length;

      final dayIndex = (6 - i).toDouble();
      incomingData.add(FlSpot(dayIndex, incoming.toDouble()));
      outgoingData.add(FlSpot(dayIndex, outgoing.toDouble()));
      missedData.add(FlSpot(dayIndex, missed.toDouble()));
    }

    return {
      'incoming': incomingData,
      'outgoing': outgoingData,
      'missed': missedData,
    };
  }

  Widget _buildChartLegendMultiSeries() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildLegendItemMultiSeries('Incoming', Colors.green),
        _buildLegendItemMultiSeries('Outgoing', Colors.blue),
        _buildLegendItemMultiSeries('Missed', Colors.red),
      ],
    );
  }

  Widget _buildLegendItemMultiSeries(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 3,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildSuccessRateTrends(List<CallLogModel> logs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Call Success Rate Trends',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: 20,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.shade200,
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          '${value.toInt()}%',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        );
                      },
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      getTitlesWidget: (value, meta) {
                        final days = [
                          'Mon',
                          'Tue',
                          'Wed',
                          'Thu',
                          'Fri',
                          'Sat',
                          'Sun'
                        ];
                        final index = value.toInt();
                        if (index >= 0 && index < days.length) {
                          return Text(
                            days[index],
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  rightTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  LineChartBarData(
                    spots: _getSuccessRateSpots(logs),
                    isCurved: true,
                    color: Colors.green,
                    barWidth: 4,
                    dotData: FlDotData(show: true),
                    belowBarData: BarAreaData(
                      show: true,
                      color: Colors.green.withValues(alpha: 0.1),
                    ),
                  ),
                ],
                minY: 0,
                maxY: 100,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<FlSpot> _getSuccessRateSpots(List<CallLogModel> logs) {
    final now = DateTime.now();
    final spots = <FlSpot>[];

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final dayLogs = logs
          .where((log) =>
              log.timestamp.isAfter(dayStart) && log.timestamp.isBefore(dayEnd))
          .toList();

      final totalCalls = dayLogs.length;
      final answeredCalls = dayLogs.where((log) => log.duration > 0).length;
      final successRate =
          totalCalls > 0 ? (answeredCalls / totalCalls * 100) : 0.0;

      spots.add(FlSpot((6 - i).toDouble(), successRate));
    }

    return spots;
  }

  Widget _buildImprovedPeakHoursChart(List<CallLogModel> logs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Peak Hours Analysis',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 220,
            child: BarChart(
              BarChartData(
                gridData: FlGridData(show: false),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: true, reservedSize: 40),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        final hour = value.toInt();
                        // Show only every 4th hour to avoid overlap
                        if (hour % 4 == 0) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              _formatHourShort(hour),
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 10,
                              ),
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  rightTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false),
                barGroups: _getImprovedHourlyBarGroups(logs),
                barTouchData: BarTouchData(
                  enabled: true,
                  touchTooltipData: BarTouchTooltipData(
                    getTooltipItem: (group, groupIndex, rod, rodIndex) {
                      return BarTooltipItem(
                        '${_formatHour(group.x.toInt())}\n${rod.toY.toInt()} calls',
                        const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatHourShort(int hour) {
    if (hour == 0) return '12a';
    if (hour < 12) return '${hour}a';
    if (hour == 12) return '12p';
    return '${hour - 12}p';
  }

  List<BarChartGroupData> _getImprovedHourlyBarGroups(List<CallLogModel> logs) {
    final hourlyCounts = <int, int>{};

    for (int hour = 0; hour < 24; hour++) {
      hourlyCounts[hour] = 0;
    }

    for (final log in logs) {
      final hour = log.timestamp.hour;
      if (hour >= 0 && hour < 24) {
        hourlyCounts[hour] = hourlyCounts[hour]! + 1;
      }
    }

    return hourlyCounts.entries
        .map((entry) => BarChartGroupData(
              x: entry.key,
              barRods: [
                BarChartRodData(
                  toY: entry.value.toDouble(),
                  color: _getHourColor(entry.key),
                  width: 12,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ))
        .toList();
  }

  Color _getHourColor(int hour) {
    if (hour >= 6 && hour < 12) return Colors.orange; // Morning
    if (hour >= 12 && hour < 18) return Colors.blue; // Afternoon
    if (hour >= 18 && hour < 22) return Colors.green; // Evening
    return Colors.purple; // Night
  }

  // Remaining analytics methods
  Widget _buildCallVolumeHeatmap(List<CallLogModel> logs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Call Volume Heatmap',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Busiest calling times by day and hour',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 20),
          _buildSimplifiedHeatmap(logs),
        ],
      ),
    );
  }

  Widget _buildSimplifiedHeatmap(List<CallLogModel> logs) {
    final dayHourData = _calculateDayHourData(logs);

    return Column(
      children: [
        // Day of week analysis
        ...List.generate(7, (dayIndex) {
          final dayNames = [
            'Monday',
            'Tuesday',
            'Wednesday',
            'Thursday',
            'Friday',
            'Saturday',
            'Sunday'
          ];
          final dayData = dayHourData[dayIndex + 1] ?? {};
          final totalCalls =
              dayData.values.fold(0, (sum, count) => sum + count);
          final peakHour = dayData.entries.isEmpty
              ? 0
              : dayData.entries.reduce((a, b) => a.value > b.value ? a : b).key;

          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        dayNames[dayIndex],
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '$totalCalls calls',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Row(
                    children: List.generate(24, (hour) {
                      final count = dayData[hour] ?? 0;
                      final maxCount = dayData.values.isEmpty
                          ? 1
                          : dayData.values.reduce((a, b) => a > b ? a : b);
                      final intensity = maxCount > 0 ? count / maxCount : 0.0;

                      return Expanded(
                        child: Container(
                          height: 16,
                          margin: const EdgeInsets.symmetric(horizontal: 0.5),
                          decoration: BoxDecoration(
                            color: _getHeatmapColor(intensity),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      );
                    }),
                  ),
                ),
                SizedBox(
                  width: 60,
                  child: Text(
                    'Peak: ${_formatHourShort(peakHour)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Map<int, Map<int, int>> _calculateDayHourData(List<CallLogModel> logs) {
    final dayHourData = <int, Map<int, int>>{};

    for (final log in logs) {
      final dayOfWeek = log.timestamp.weekday;
      final hour = log.timestamp.hour;

      dayHourData[dayOfWeek] ??= <int, int>{};
      dayHourData[dayOfWeek]![hour] = (dayHourData[dayOfWeek]![hour] ?? 0) + 1;
    }

    return dayHourData;
  }

  Color _getHeatmapColor(double intensity) {
    if (intensity == 0) return Colors.grey.shade200;
    if (intensity < 0.3) return Colors.blue.shade200;
    if (intensity < 0.6) return Colors.blue.shade400;
    return Colors.blue.shade600;
  }

  Widget _buildDurationByTimeAnalysis(List<CallLogModel> logs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Call Duration by Time of Day',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          _buildTimeSlotAnalysis(logs),
        ],
      ),
    );
  }

  Widget _buildTimeSlotAnalysis(List<CallLogModel> logs) {
    final timeSlotData = _calculateTimeSlotData(logs);

    return Column(
      children: timeSlotData.entries.map((entry) {
        final slotName = entry.key;
        final data = entry.value;
        final avgDuration = data['avgDuration'] as double;
        final totalCalls = data['totalCalls'] as int;

        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    slotName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '${avgDuration.toStringAsFixed(1)} min avg',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: avgDuration / 10, // Assuming max 10 minutes for scale
                backgroundColor: Colors.grey.shade200,
                valueColor:
                    AlwaysStoppedAnimation<Color>(_getTimeSlotColor(slotName)),
                minHeight: 8,
              ),
              const SizedBox(height: 4),
              Text(
                '$totalCalls calls',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Map<String, Map<String, dynamic>> _calculateTimeSlotData(
      List<CallLogModel> logs) {
    final timeSlots = {
      'Morning (6AM-12PM)': <int>[],
      'Afternoon (12PM-6PM)': <int>[],
      'Evening (6PM-10PM)': <int>[],
      'Night (10PM-6AM)': <int>[],
    };

    for (final log in logs) {
      if (log.duration > 0) {
        final hour = log.timestamp.hour;
        String slot;
        if (hour >= 6 && hour < 12)
          slot = 'Morning (6AM-12PM)';
        else if (hour >= 12 && hour < 18)
          slot = 'Afternoon (12PM-6PM)';
        else if (hour >= 18 && hour < 22)
          slot = 'Evening (6PM-10PM)';
        else
          slot = 'Night (10PM-6AM)';

        timeSlots[slot]!.add(log.duration);
      }
    }

    final result = <String, Map<String, dynamic>>{};
    for (final entry in timeSlots.entries) {
      final durations = entry.value;
      final avgDuration = durations.isNotEmpty
          ? durations.reduce((a, b) => a + b) /
              durations.length /
              60 // Convert to minutes
          : 0.0;

      result[entry.key] = {
        'avgDuration': avgDuration,
        'totalCalls': durations.length,
      };
    }

    return result;
  }

  Color _getTimeSlotColor(String slot) {
    if (slot.contains('Morning')) return Colors.orange;
    if (slot.contains('Afternoon')) return Colors.blue;
    if (slot.contains('Evening')) return Colors.green;
    return Colors.purple;
  }

  Widget _buildComparativeAnalytics(List<CallLogModel> allLogs) {
    final comparison = _calculateComparativeData(allLogs);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Comparative Analysis',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildComparisonCard(
                  'This Week vs Last Week',
                  comparison['weeklyChange'] ?? '0%',
                  comparison['weeklyTrend'] ?? 'up',
                  Icons.calendar_view_week,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildComparisonCard(
                  'This Month vs Last Month',
                  comparison['monthlyChange'] ?? '0%',
                  comparison['monthlyTrend'] ?? 'up',
                  Icons.calendar_month,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonCard(
      String title, String change, String trend, IconData icon) {
    final isPositive = trend == 'up';
    final color = isPositive ? Colors.green : Colors.red;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                isPositive ? Icons.trending_up : Icons.trending_down,
                color: color,
                size: 20,
              ),
              const SizedBox(width: 4),
              Text(
                change,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Map<String, String> _calculateComparativeData(List<CallLogModel> allLogs) {
    final now = DateTime.now();

    // This week vs last week
    final thisWeekStart = now.subtract(Duration(days: now.weekday - 1));
    final thisWeekEnd = thisWeekStart.add(const Duration(days: 7));
    final lastWeekStart = thisWeekStart.subtract(const Duration(days: 7));
    final lastWeekEnd = thisWeekStart;

    final thisWeekCalls = allLogs
        .where((log) =>
            log.timestamp.isAfter(thisWeekStart) &&
            log.timestamp.isBefore(thisWeekEnd))
        .length;

    final lastWeekCalls = allLogs
        .where((log) =>
            log.timestamp.isAfter(lastWeekStart) &&
            log.timestamp.isBefore(lastWeekEnd))
        .length;

    final weeklyChange = lastWeekCalls > 0
        ? ((thisWeekCalls - lastWeekCalls) / lastWeekCalls * 100).round()
        : thisWeekCalls > 0
            ? 100
            : 0;

    // This month vs last month
    final thisMonthStart = DateTime(now.year, now.month, 1);
    final lastMonthStart = DateTime(now.year, now.month - 1, 1);
    final lastMonthEnd = thisMonthStart;

    final thisMonthCalls =
        allLogs.where((log) => log.timestamp.isAfter(thisMonthStart)).length;

    final lastMonthCalls = allLogs
        .where((log) =>
            log.timestamp.isAfter(lastMonthStart) &&
            log.timestamp.isBefore(lastMonthEnd))
        .length;

    final monthlyChange = lastMonthCalls > 0
        ? ((thisMonthCalls - lastMonthCalls) / lastMonthCalls * 100).round()
        : thisMonthCalls > 0
            ? 100
            : 0;

    return {
      'weeklyChange': '${weeklyChange.abs()}%',
      'weeklyTrend': weeklyChange >= 0 ? 'up' : 'down',
      'monthlyChange': '${monthlyChange.abs()}%',
      'monthlyTrend': monthlyChange >= 0 ? 'up' : 'down',
    };
  }

  Widget _buildEnhancedTopContacts(List<CallLogModel> logs) {
    if (logs.isEmpty) {
      return _buildEmptyTopContacts();
    }

    final contactAnalytics = _calculateContactAnalytics(logs);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Top Contacts & Engagement',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 16),
          ...contactAnalytics.take(5).map((contact) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: Theme.of(context)
                            .primaryColor
                            .withValues(alpha: 0.1),
                        child: Text(
                          _getContactInitial(contact['name']),
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              contact['name'],
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              '${contact['totalCalls']} calls • ${_formatDuration(contact['totalDuration'])}',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        '${contact['answerRate']}%',
                        style: TextStyle(
                          color: contact['answerRate'] > 70
                              ? Colors.green
                              : Colors.orange,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      _buildContactStat(
                          'Incoming', contact['incoming'], Colors.green),
                      _buildContactStat(
                          'Outgoing', contact['outgoing'], Colors.blue),
                      _buildContactStat(
                          'Missed', contact['missed'], Colors.red),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildContactStat(String label, int count, Color color) {
    return Expanded(
      child: Column(
        children: [
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _calculateContactAnalytics(
      List<CallLogModel> logs) {
    final contactData = <String, Map<String, dynamic>>{};

    for (final log in logs) {
      final key = log.name ?? log.number;
      if (key.isEmpty) continue;

      if (!contactData.containsKey(key)) {
        contactData[key] = {
          'name': log.name ?? log.number,
          'totalCalls': 0,
          'totalDuration': 0,
          'incoming': 0,
          'outgoing': 0,
          'missed': 0,
          'answered': 0,
        };
      }

      final contact = contactData[key]!;
      contact['totalCalls']++;
      contact['totalDuration'] += log.duration;

      switch (log.type) {
        case CallType.incoming:
          contact['incoming']++;
          if (log.duration > 0) contact['answered']++;
          break;
        case CallType.outgoing:
          contact['outgoing']++;
          if (log.duration > 0) contact['answered']++;
          break;
        case CallType.missed:
          contact['missed']++;
          break;
        default:
          break;
      }
    }

    // Calculate answer rates and sort
    final result = contactData.values.map((contact) {
      final totalCalls = contact['totalCalls'] as int;
      final answered = contact['answered'] as int;
      contact['answerRate'] =
          totalCalls > 0 ? ((answered / totalCalls) * 100).round() : 0;
      return contact;
    }).toList();

    result.sort(
        (a, b) => (b['totalCalls'] as int).compareTo(a['totalCalls'] as int));
    return result;
  }

  void _shareAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Analytics sharing feature coming soon!'),
      ),
    );
  }
}
