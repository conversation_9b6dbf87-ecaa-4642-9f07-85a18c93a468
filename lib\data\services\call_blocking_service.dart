import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';
import '../models/blocked_number_model.dart';

class CallBlockingService {
  static final CallBlockingService _instance = CallBlockingService._internal();
  factory CallBlockingService() => _instance;
  CallBlockingService._internal();

  static const String _blockedNumbersBoxName = 'blocked_numbers';
  static const String _spamPatternsKey = 'spam_patterns';
  static const String _blockUnknownKey = 'block_unknown_numbers';
  static const String _blockPrivateKey = 'block_private_numbers';

  Future<List<BlockedNumber>> getBlockedNumbers() async {
    try {
      final box = await Hive.openBox<BlockedNumber>(_blockedNumbersBoxName);
      return box.values.toList();
    } catch (e) {
      debugPrint('Error getting blocked numbers: $e');
      return [];
    }
  }

  Future<void> blockNumber(String phoneNumber, {String? reason}) async {
    try {
      final box = await Hive.openBox<BlockedNumber>(_blockedNumbersBoxName);
      final cleanNumber = _cleanPhoneNumber(phoneNumber);
      
      final blockedNumber = BlockedNumber(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        phoneNumber: cleanNumber,
        originalNumber: phoneNumber,
        reason: reason ?? 'Manually blocked',
        blockedAt: DateTime.now(),
      );

      await box.put(cleanNumber, blockedNumber);
      debugPrint('Blocked number: $phoneNumber');
    } catch (e) {
      debugPrint('Error blocking number: $e');
      rethrow;
    }
  }

  Future<void> unblockNumber(String phoneNumber) async {
    try {
      final box = await Hive.openBox<BlockedNumber>(_blockedNumbersBoxName);
      final cleanNumber = _cleanPhoneNumber(phoneNumber);
      
      await box.delete(cleanNumber);
      debugPrint('Unblocked number: $phoneNumber');
    } catch (e) {
      debugPrint('Error unblocking number: $e');
      rethrow;
    }
  }

  Future<bool> isNumberBlocked(String phoneNumber) async {
    try {
      final box = await Hive.openBox<BlockedNumber>(_blockedNumbersBoxName);
      final cleanNumber = _cleanPhoneNumber(phoneNumber);
      
      return box.containsKey(cleanNumber);
    } catch (e) {
      debugPrint('Error checking if number is blocked: $e');
      return false;
    }
  }

  Future<BlockedNumber?> getBlockedNumberInfo(String phoneNumber) async {
    try {
      final box = await Hive.openBox<BlockedNumber>(_blockedNumbersBoxName);
      final cleanNumber = _cleanPhoneNumber(phoneNumber);
      
      return box.get(cleanNumber);
    } catch (e) {
      debugPrint('Error getting blocked number info: $e');
      return null;
    }
  }

  Future<bool> shouldBlockCall(String phoneNumber, {String? contactName}) async {
    try {
      // Check if number is explicitly blocked
      if (await isNumberBlocked(phoneNumber)) {
        return true;
      }

      // Check if unknown numbers should be blocked
      if (contactName == null && await shouldBlockUnknownNumbers()) {
        return true;
      }

      // Check if private numbers should be blocked
      if (_isPrivateNumber(phoneNumber) && await shouldBlockPrivateNumbers()) {
        return true;
      }

      // Check spam patterns
      if (await _matchesSpamPattern(phoneNumber)) {
        // Auto-block suspected spam
        await blockNumber(phoneNumber, reason: 'Suspected spam');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error checking if call should be blocked: $e');
      return false;
    }
  }

  Future<bool> shouldBlockUnknownNumbers() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_blockUnknownKey) ?? false;
  }

  Future<void> setBlockUnknownNumbers(bool block) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_blockUnknownKey, block);
  }

  Future<bool> shouldBlockPrivateNumbers() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_blockPrivateKey) ?? false;
  }

  Future<void> setBlockPrivateNumbers(bool block) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_blockPrivateKey, block);
  }

  Future<List<String>> getSpamPatterns() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_spamPatternsKey) ?? _getDefaultSpamPatterns();
  }

  Future<void> addSpamPattern(String pattern) async {
    final patterns = await getSpamPatterns();
    if (!patterns.contains(pattern)) {
      patterns.add(pattern);
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_spamPatternsKey, patterns);
    }
  }

  Future<void> removeSpamPattern(String pattern) async {
    final patterns = await getSpamPatterns();
    patterns.remove(pattern);
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_spamPatternsKey, patterns);
  }

  Future<bool> _matchesSpamPattern(String phoneNumber) async {
    final patterns = await getSpamPatterns();
    final cleanNumber = _cleanPhoneNumber(phoneNumber);

    for (final pattern in patterns) {
      if (_matchesPattern(cleanNumber, pattern)) {
        return true;
      }
    }

    return false;
  }

  bool _matchesPattern(String number, String pattern) {
    // Simple pattern matching - can be enhanced with regex
    if (pattern.contains('*')) {
      // Wildcard pattern
      final regexPattern = pattern.replaceAll('*', '.*');
      return RegExp(regexPattern).hasMatch(number);
    } else {
      // Exact or prefix match
      return number.startsWith(pattern);
    }
  }

  List<String> _getDefaultSpamPatterns() {
    return [
      // Common spam prefixes
      '1800*', // Toll-free numbers
      '1888*',
      '1877*',
      '1866*',
      '1855*',
      '1844*',
      '1833*',
      '1822*',
      // Robocall patterns
      '*0000', // Numbers ending in 0000
      '*1111',
      '*2222',
      '*3333',
      '*4444',
      '*5555',
      '*6666',
      '*7777',
      '*8888',
      '*9999',
      // Sequential patterns
      '1234567*',
      '7654321*',
    ];
  }

  bool _isPrivateNumber(String phoneNumber) {
    final cleanNumber = _cleanPhoneNumber(phoneNumber);
    return cleanNumber.isEmpty || 
           cleanNumber == 'unknown' || 
           cleanNumber == 'private' ||
           cleanNumber == 'blocked';
  }

  String _cleanPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters except +
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Handle international format
    if (cleaned.startsWith('+1') && cleaned.length == 12) {
      cleaned = cleaned.substring(2); // Remove +1 for US numbers
    } else if (cleaned.startsWith('1') && cleaned.length == 11) {
      cleaned = cleaned.substring(1); // Remove leading 1 for US numbers
    }
    
    return cleaned;
  }

  Future<Map<String, int>> getBlockingStats() async {
    try {
      final blockedNumbers = await getBlockedNumbers();
      final stats = <String, int>{};

      // Count by reason
      for (final blocked in blockedNumbers) {
        final reason = blocked.reason;
        stats[reason] = (stats[reason] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      debugPrint('Error getting blocking stats: $e');
      return {};
    }
  }

  Future<void> importBlockedNumbers(List<String> phoneNumbers) async {
    try {
      for (final number in phoneNumbers) {
        await blockNumber(number, reason: 'Imported');
      }
    } catch (e) {
      debugPrint('Error importing blocked numbers: $e');
      rethrow;
    }
  }

  Future<List<String>> exportBlockedNumbers() async {
    try {
      final blockedNumbers = await getBlockedNumbers();
      return blockedNumbers.map((blocked) => blocked.originalNumber).toList();
    } catch (e) {
      debugPrint('Error exporting blocked numbers: $e');
      return [];
    }
  }

  Future<void> clearAllBlockedNumbers() async {
    try {
      final box = await Hive.openBox<BlockedNumber>(_blockedNumbersBoxName);
      await box.clear();
      debugPrint('Cleared all blocked numbers');
    } catch (e) {
      debugPrint('Error clearing blocked numbers: $e');
      rethrow;
    }
  }

  Future<void> reportSpam(String phoneNumber, String reason) async {
    try {
      // Block the number
      await blockNumber(phoneNumber, reason: 'Reported as spam: $reason');
      
      // Add to spam patterns if it matches a pattern
      final cleanNumber = _cleanPhoneNumber(phoneNumber);
      if (cleanNumber.length >= 6) {
        final prefix = cleanNumber.substring(0, 6);
        await addSpamPattern('$prefix*');
      }
      
      debugPrint('Reported spam: $phoneNumber');
    } catch (e) {
      debugPrint('Error reporting spam: $e');
      rethrow;
    }
  }
}
