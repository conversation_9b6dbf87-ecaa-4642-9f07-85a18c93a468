import 'package:hive/hive.dart';
import 'dart:typed_data';

part 'contact_model.g.dart';

@HiveType(typeId: 5)
class ContactModel {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String displayName;

  @HiveField(2)
  final String? givenName;

  @HiveField(3)
  final String? familyName;

  @HiveField(4)
  final List<PhoneNumber> phoneNumbers;

  @HiveField(5)
  final List<EmailAddress> emailAddresses;

  @HiveField(6)
  final Uint8List? avatar;

  @HiveField(7)
  final bool isFavorite;

  @HiveField(8)
  final DateTime? lastContactedDate;

  @HiveField(9)
  final int contactFrequency;

  ContactModel({
    required this.id,
    required this.displayName,
    this.givenName,
    this.familyName,
    required this.phoneNumbers,
    required this.emailAddresses,
    this.avatar,
    this.isFavorite = false,
    this.lastContactedDate,
    this.contactFrequency = 0,
  });

  factory ContactModel.fromJson(Map<String, dynamic> json) {
    return ContactModel(
      id: json['id'] as String,
      displayName: json['display_name'] as String,
      givenName: json['given_name'] as String?,
      familyName: json['family_name'] as String?,
      phoneNumbers: (json['phone_numbers'] as List<dynamic>?)
              ?.map((e) => PhoneNumber.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      emailAddresses: (json['email_addresses'] as List<dynamic>?)
              ?.map((e) => EmailAddress.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      avatar: json['avatar'] != null ? Uint8List.fromList(json['avatar']) : null,
      isFavorite: json['is_favorite'] as bool? ?? false,
      lastContactedDate: json['last_contacted_date'] != null
          ? DateTime.parse(json['last_contacted_date'] as String)
          : null,
      contactFrequency: json['contact_frequency'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'display_name': displayName,
      'given_name': givenName,
      'family_name': familyName,
      'phone_numbers': phoneNumbers.map((e) => e.toJson()).toList(),
      'email_addresses': emailAddresses.map((e) => e.toJson()).toList(),
      'avatar': avatar?.toList(),
      'is_favorite': isFavorite,
      'last_contacted_date': lastContactedDate?.toIso8601String(),
      'contact_frequency': contactFrequency,
    };
  }

  ContactModel copyWith({
    String? id,
    String? displayName,
    String? givenName,
    String? familyName,
    List<PhoneNumber>? phoneNumbers,
    List<EmailAddress>? emailAddresses,
    Uint8List? avatar,
    bool? isFavorite,
    DateTime? lastContactedDate,
    int? contactFrequency,
  }) {
    return ContactModel(
      id: id ?? this.id,
      displayName: displayName ?? this.displayName,
      givenName: givenName ?? this.givenName,
      familyName: familyName ?? this.familyName,
      phoneNumbers: phoneNumbers ?? this.phoneNumbers,
      emailAddresses: emailAddresses ?? this.emailAddresses,
      avatar: avatar ?? this.avatar,
      isFavorite: isFavorite ?? this.isFavorite,
      lastContactedDate: lastContactedDate ?? this.lastContactedDate,
      contactFrequency: contactFrequency ?? this.contactFrequency,
    );
  }
}

@HiveType(typeId: 6)
class PhoneNumber {
  @HiveField(0)
  final String number;

  @HiveField(1)
  final String label;

  @HiveField(2)
  final bool isPrimary;

  PhoneNumber({
    required this.number,
    required this.label,
    this.isPrimary = false,
  });

  factory PhoneNumber.fromJson(Map<String, dynamic> json) {
    return PhoneNumber(
      number: json['number'] as String,
      label: json['label'] as String,
      isPrimary: json['is_primary'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'label': label,
      'is_primary': isPrimary,
    };
  }
}

@HiveType(typeId: 7)
class EmailAddress {
  @HiveField(0)
  final String address;

  @HiveField(1)
  final String label;

  @HiveField(2)
  final bool isPrimary;

  EmailAddress({
    required this.address,
    required this.label,
    this.isPrimary = false,
  });

  factory EmailAddress.fromJson(Map<String, dynamic> json) {
    return EmailAddress(
      address: json['address'] as String,
      label: json['label'] as String,
      isPrimary: json['is_primary'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'label': label,
      'is_primary': isPrimary,
    };
  }
}
