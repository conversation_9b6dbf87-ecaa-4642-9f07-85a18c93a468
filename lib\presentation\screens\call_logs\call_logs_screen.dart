import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import '../../providers/call_log_provider.dart';
import '../../providers/lead_provider.dart';
import '../../../data/models/call_log_model.dart';
import '../../../data/models/lead_model.dart';
import '../../widgets/call_classification_dialog.dart';

class CallLogsScreen extends StatefulWidget {
  const CallLogsScreen({super.key});

  @override
  State<CallLogsScreen> createState() => _CallLogsScreenState();
}

class _CallLogsScreenState extends State<CallLogsScreen>
    with SingleTickerProviderStateMixin {
  CallType? _selectedFilter;
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  // Search and Filter States
  bool _isSearching = false;
  String _searchQuery = '';
  String _selectedTimeFilter = 'All';
  String _selectedDurationFilter = 'All';
  bool _showOnlyUnsynced = false;
  bool _showAdvancedFilters = false;
  bool _isFiltering = false;

  // Date Range
  DateTime? _startDate;
  DateTime? _endDate;

  // Filter Options
  final List<String> _timeFilters = [
    'All',
    'Today',
    'Yesterday',
    'This Week',
    'This Month',
    'Custom Range'
  ];
  final List<String> _durationFilters = [
    'All',
    'Quick (<30s)',
    'Short (30s-2m)',
    'Medium (2m-10m)',
    'Long (>10m)',
    'No Answer'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _searchController.addListener(_onSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CallLogProvider>().loadCallLogs(context);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _searchQuery = '';
      }
    });
  }

  void _clearAllFilters() {
    setState(() {
      _selectedFilter = null;
      _selectedTimeFilter = 'All';
      _selectedDurationFilter = 'All';
      _showOnlyUnsynced = false;
      _startDate = null;
      _endDate = null;
      _searchQuery = '';
    });
    _searchController.clear();
    // Don't use provider filtering, let local filtering handle it
    // context.read<CallLogProvider>().filterByType(null);
  }

  List<CallLogModel> _getFilteredCallLogs(List<CallLogModel> allLogs) {
    List<CallLogModel> filteredLogs = allLogs;

    // Apply call type filter (this should be the base filtered list from provider)
    if (_selectedFilter != null) {
      filteredLogs =
          filteredLogs.where((log) => log.type == _selectedFilter).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filteredLogs = filteredLogs.where((log) {
        final name = log.name?.toLowerCase() ?? '';
        final number = log.number.toLowerCase();
        final query = _searchQuery.toLowerCase();
        return name.contains(query) || number.contains(query);
      }).toList();
    }

    // Apply time filter
    if (_selectedTimeFilter != 'All') {
      final now = DateTime.now();
      DateTime? startTime;
      DateTime? endTime;

      switch (_selectedTimeFilter) {
        case 'Today':
          startTime = DateTime(now.year, now.month, now.day);
          endTime = startTime.add(const Duration(days: 1));
          break;
        case 'Yesterday':
          startTime = DateTime(now.year, now.month, now.day - 1);
          endTime = startTime.add(const Duration(days: 1));
          break;
        case 'This Week':
          startTime = now.subtract(Duration(days: now.weekday - 1));
          startTime = DateTime(startTime.year, startTime.month, startTime.day);
          endTime = startTime.add(const Duration(days: 7));
          break;
        case 'This Month':
          startTime = DateTime(now.year, now.month, 1);
          endTime = DateTime(now.year, now.month + 1, 1);
          break;
        case 'Custom Range':
          startTime = _startDate;
          endTime = _endDate;
          break;
      }

      if (startTime != null && endTime != null) {
        filteredLogs = filteredLogs.where((log) {
          return log.timestamp.isAfter(startTime!) &&
              log.timestamp.isBefore(endTime!);
        }).toList();
      }
    }

    // Apply duration filter
    if (_selectedDurationFilter != 'All') {
      switch (_selectedDurationFilter) {
        case 'Quick (<30s)':
          filteredLogs =
              filteredLogs.where((log) => log.duration < 30).toList();
          break;
        case 'Short (30s-2m)':
          filteredLogs = filteredLogs
              .where((log) => log.duration >= 30 && log.duration < 120)
              .toList();
          break;
        case 'Medium (2m-10m)':
          filteredLogs = filteredLogs
              .where((log) => log.duration >= 120 && log.duration < 600)
              .toList();
          break;
        case 'Long (>10m)':
          filteredLogs =
              filteredLogs.where((log) => log.duration >= 600).toList();
          break;
        case 'No Answer':
          filteredLogs =
              filteredLogs.where((log) => log.duration == 0).toList();
          break;
      }
    }

    // Apply sync filter
    if (_showOnlyUnsynced) {
      filteredLogs = filteredLogs.where((log) => !log.isSynced).toList();
    }

    return filteredLogs;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: 'Search by name or number...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(color: Colors.grey.shade400),
                ),
                style: const TextStyle(fontSize: 18),
              )
            : const Text(
                'Call Logs',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 24,
                ),
              ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search_rounded),
            onPressed: _toggleSearch,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert_rounded),
            onSelected: (value) {
              switch (value) {
                case 'sort_recent':
                  // Sort by recent
                  break;
                case 'sort_duration':
                  // Sort by duration
                  break;
                case 'export':
                  _exportCallLogs();
                  break;
                case 'clear_filters':
                  _clearAllFilters();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'sort_recent',
                child: Row(
                  children: [
                    Icon(Icons.sort_rounded),
                    SizedBox(width: 8),
                    Text('Sort by Recent'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sort_duration',
                child: Row(
                  children: [
                    Icon(Icons.timer_rounded),
                    SizedBox(width: 8),
                    Text('Sort by Duration'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download_rounded),
                    SizedBox(width: 8),
                    Text('Export Logs'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_filters',
                child: Row(
                  children: [
                    Icon(Icons.clear_all_rounded),
                    SizedBox(width: 8),
                    Text('Clear Filters'),
                  ],
                ),
              ),
            ],
          ),
          Consumer<CallLogProvider>(
            builder: (context, provider, child) {
              return Stack(
                children: [
                  IconButton(
                    icon: Icon(
                      provider.isSyncing ? Icons.sync : Icons.sync_rounded,
                      color: provider.isSyncing ? Colors.orange : Colors.black,
                    ),
                    onPressed: provider.isSyncing
                        ? null
                        : () => provider.syncCallLogs(),
                  ),
                  if (provider.isSyncing)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.orange,
                          ),
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Results Info
          if (_searchQuery.isNotEmpty)
            Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Icon(Icons.search_rounded,
                      color: Colors.grey.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Consumer<CallLogProvider>(
                      builder: (context, provider, child) {
                        final filteredCount =
                            _getFilteredCallLogs(provider.callLogs).length;
                        return Text(
                          'Found $filteredCount results for "$_searchQuery"',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 14,
                          ),
                        );
                      },
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      _searchController.clear();
                      _searchQuery = '';
                    },
                    child: const Text('Clear'),
                  ),
                ],
              ),
            ),

          // Enhanced Filter Section
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Filters',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    Row(
                      children: [
                        TextButton(
                          onPressed: _clearAllFilters,
                          child: const Text('Clear All'),
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _showAdvancedFilters = !_showAdvancedFilters;
                            });
                          },
                          icon: Icon(
                            _showAdvancedFilters
                                ? Icons.expand_less_rounded
                                : Icons.expand_more_rounded,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // Call Type Filters
                Container(
                  height: 40,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      _buildFilterChip('All', null),
                      const SizedBox(width: 8),
                      _buildFilterChip('Incoming', CallType.incoming),
                      const SizedBox(width: 8),
                      _buildFilterChip('Outgoing', CallType.outgoing),
                      const SizedBox(width: 8),
                      _buildFilterChip('Missed', CallType.missed),
                      const SizedBox(width: 8),
                      _buildFilterChip('Rejected', CallType.rejected),
                    ],
                  ),
                ),

                // Advanced Filters
                if (_showAdvancedFilters) ...[
                  const SizedBox(height: 16),

                  // Time Filter
                  Row(
                    children: [
                      Icon(Icons.schedule_rounded,
                          color: Colors.grey.shade600, size: 20),
                      const SizedBox(width: 8),
                      const Text('Time:',
                          style: TextStyle(fontWeight: FontWeight.w500)),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButton<String>(
                          value: _selectedTimeFilter,
                          underline: Container(),
                          isExpanded: true,
                          items: _timeFilters.map((filter) {
                            return DropdownMenuItem(
                              value: filter,
                              child: Text(filter),
                            );
                          }).toList(),
                          onChanged: (value) async {
                            if (value == 'Custom Range') {
                              await _selectDateRange();
                            } else {
                              setState(() {
                                _selectedTimeFilter = value!;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Duration Filter
                  Row(
                    children: [
                      Icon(Icons.timer_rounded,
                          color: Colors.grey.shade600, size: 20),
                      const SizedBox(width: 8),
                      const Text('Duration:',
                          style: TextStyle(fontWeight: FontWeight.w500)),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButton<String>(
                          value: _selectedDurationFilter,
                          underline: Container(),
                          isExpanded: true,
                          items: _durationFilters.map((filter) {
                            return DropdownMenuItem(
                              value: filter,
                              child: Text(filter),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedDurationFilter = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Custom Date Range Display
                  if (_selectedTimeFilter == 'Custom Range' &&
                      _startDate != null &&
                      _endDate != null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.date_range_rounded,
                              color: Theme.of(context).primaryColor, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            '${DateFormat('MMM dd, yyyy').format(_startDate!)} - ${DateFormat('MMM dd, yyyy').format(_endDate!)}',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Additional Filters
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Checkbox(
                        value: _showOnlyUnsynced,
                        onChanged: (value) {
                          setState(() {
                            _showOnlyUnsynced = value!;
                          });
                        },
                      ),
                      const Text('Show only unsynced calls'),
                    ],
                  ),
                ],
              ],
            ),
          ),

          // Active Filters Summary
          if (_hasActiveFilters())
            Container(
              color: Colors.white,
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.filter_list_rounded,
                            color: Colors.blue.shade700, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Active Filters:',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: _getActiveFilterChips(),
                    ),
                  ],
                ),
              ),
            ),

          // Stats Summary
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
            child: Consumer<CallLogProvider>(
              builder: (context, provider, child) {
                final filteredLogs = _getFilteredCallLogs(provider.callLogs);
                final stats = _calculateFilteredStats(filteredLogs);
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor.withOpacity(0.1),
                        Theme.of(context).primaryColor.withOpacity(0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(
                        'Total',
                        stats['total'].toString(),
                        Icons.call_rounded,
                        Colors.blue,
                      ),
                      _buildStatItem(
                        'Outgoing',
                        stats['outgoing'].toString(),
                        Icons.call_made_rounded,
                        Colors.green,
                      ),
                      _buildStatItem(
                        'Incoming',
                        stats['incoming'].toString(),
                        Icons.call_received_rounded,
                        Colors.purple,
                      ),
                      _buildStatItem(
                        'Missed',
                        stats['missed'].toString(),
                        Icons.call_received_rounded,
                        Colors.red,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          // Call Logs List
          Expanded(
            child: Consumer<CallLogProvider>(
              builder: (context, callLogProvider, child) {
                if (callLogProvider.isLoading) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Loading call logs...',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final filteredLogs =
                    _getFilteredCallLogs(callLogProvider.callLogs);

                if (filteredLogs.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () => callLogProvider.loadCallLogs(context),
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredLogs.length,
                    itemBuilder: (context, index) {
                      final callLog = filteredLogs[index];
                      return _EnhancedCallLogCard(
                        callLog: callLog,
                        onCall: () => _makeCall(callLog.number),
                        onSMS: () => _sendSMS(callLog.number),
                        onWhatsApp: () => _openWhatsApp(callLog.number),
                        onClassify: () => _showCallClassification(callLog),
                        index: index,
                        searchQuery: _searchQuery,
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, CallType? type) {
    final isSelected = _selectedFilter == type;
    return GestureDetector(
      onTap: () {
        setState(() {
          // Toggle filter: if already selected, deselect it
          _selectedFilter = _selectedFilter == type ? null : type;
        });
        // Don't use provider filtering, let local filtering handle it
        // context.read<CallLogProvider>().filterByType(_selectedFilter);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor
              : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.shade300,
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color:
                        Theme.of(context).primaryColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isSelected) ...[
              Icon(
                Icons.check_circle,
                size: 16,
                color: Colors.white,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey.shade700,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    final hasFilters = _hasActiveFilters();
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              hasFilters ? Icons.filter_list_off_rounded : Icons.call_outlined,
              size: 60,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            hasFilters ? 'No calls match your filters' : 'No call logs found',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            hasFilters
                ? 'Try adjusting your search or filters'
                : 'Your call history will appear here',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 32),
          if (hasFilters)
            ElevatedButton.icon(
              onPressed: _clearAllFilters,
              icon: const Icon(Icons.clear_all_rounded),
              label: const Text('Clear All Filters'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            )
          else
            ElevatedButton.icon(
              onPressed: () =>
                  context.read<CallLogProvider>().loadCallLogs(context),
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('Refresh'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
        ],
      ),
    );
  }

  bool _hasActiveFilters() {
    return _selectedFilter != null ||
        _selectedTimeFilter != 'All' ||
        _selectedDurationFilter != 'All' ||
        _showOnlyUnsynced ||
        _searchQuery.isNotEmpty;
  }

  List<Widget> _getActiveFilterChips() {
    List<Widget> chips = [];

    if (_selectedFilter != null) {
      chips.add(
          _buildActiveFilterChip(_getCallTypeDisplayName(_selectedFilter!)));
    }

    if (_selectedTimeFilter != 'All') {
      chips.add(_buildActiveFilterChip(_selectedTimeFilter));
    }

    if (_selectedDurationFilter != 'All') {
      chips.add(_buildActiveFilterChip(_selectedDurationFilter));
    }

    if (_showOnlyUnsynced) {
      chips.add(_buildActiveFilterChip('Unsynced Only'));
    }

    if (_searchQuery.isNotEmpty) {
      chips.add(_buildActiveFilterChip('Search: "$_searchQuery"'));
    }

    return chips;
  }

  Widget _buildActiveFilterChip(String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          color: Colors.blue.shade700,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Map<String, int> _calculateFilteredStats(List<CallLogModel> logs) {
    return {
      'total': logs.length,
      'outgoing': logs.where((log) => log.type == CallType.outgoing).length,
      'incoming': logs.where((log) => log.type == CallType.incoming).length,
      'missed': logs.where((log) => log.type == CallType.missed).length,
    };
  }

  String _getCallTypeDisplayName(CallType type) {
    switch (type) {
      case CallType.incoming:
        return 'Incoming';
      case CallType.outgoing:
        return 'Outgoing';
      case CallType.missed:
        return 'Missed';
      case CallType.rejected:
        return 'Rejected';
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
        _selectedTimeFilter = 'Custom Range';
      });
    }
  }

  void _exportCallLogs() {
    // Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality will be implemented'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _makeCall(String phone) {
    launchUrl(Uri.parse('tel:$phone'));
  }

  void _sendSMS(String phone) {
    launchUrl(Uri.parse('sms:$phone'));
  }

  void _openWhatsApp(String phone) {
    launchUrl(Uri.parse('https://wa.me/$phone'));
  }

  void _showCallClassification(CallLogModel callLog) {
    final leadProvider = context.read<LeadProvider>();
    final callLogProvider = context.read<CallLogProvider>();
    final existingLead = _findExistingLead(callLog.number, leadProvider);
    final currentClassification =
        callLogProvider.getCallClassification(callLog.id);

    showDialog(
      context: context,
      builder: (context) => CallClassificationDialog(
        callLog: callLog,
        existingLead: existingLead,
        currentClassification: currentClassification,
        onClassified: (isBusinessCall) {
          _handleClassification(callLog, isBusinessCall, callLogProvider);
        },
        onLeadCreated: (lead) {
          _handleLeadCreated(lead, leadProvider);
        },
        onLeadUpdated: (lead, newStatus) {
          _handleLeadUpdated(lead, newStatus, leadProvider);
        },
      ),
    );
  }

  LeadModel? _findExistingLead(String phoneNumber, LeadProvider leadProvider) {
    try {
      return leadProvider.leads.firstWhere(
        (lead) => lead.phone == phoneNumber,
      );
    } catch (e) {
      return null; // No existing lead found
    }
  }

  void _handleClassification(CallLogModel callLog, bool isBusinessCall,
      CallLogProvider callLogProvider) {
    final newClassification = isBusinessCall
        ? CallClassification.business
        : CallClassification.personal;
    final currentClassification =
        callLogProvider.getCallClassification(callLog.id);

    // Handle re-classification scenarios
    if (currentClassification != null &&
        currentClassification != newClassification) {
      _handleReclassification(
          callLog, currentClassification, newClassification, callLogProvider);
    } else {
      // Store the classification
      callLogProvider.classifyCall(callLog.id, newClassification);

      if (isBusinessCall) {
        _showSuccessMessage('Call classified as business call!');
      } else {
        _showSuccessMessage('Call classified as personal call.');
      }
    }
  }

  void _handleReclassification(
      CallLogModel callLog,
      CallClassification oldClassification,
      CallClassification newClassification,
      CallLogProvider callLogProvider) {
    if (oldClassification == CallClassification.business &&
        newClassification == CallClassification.personal) {
      // Changing from business to personal - ask about lead deletion
      _showLeadDeletionDialog(callLog, newClassification, callLogProvider);
    } else {
      // Store the new classification
      callLogProvider.classifyCall(callLog.id, newClassification);

      if (newClassification == CallClassification.business) {
        _showSuccessMessage('Call re-classified as business call!');
      } else {
        _showSuccessMessage('Call re-classified as personal call.');
      }
    }
  }

  void _showLeadDeletionDialog(CallLogModel callLog,
      CallClassification newClassification, CallLogProvider callLogProvider) {
    final leadProvider = context.read<LeadProvider>();
    final existingLead = _findExistingLead(callLog.number, leadProvider);

    if (existingLead != null) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Lead Management'),
          content: Text('This call is being changed from Business to Personal. '
              'There is an existing lead for ${existingLead.name}. '
              'What would you like to do with the lead?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Keep lead, just reclassify call
                callLogProvider.classifyCall(callLog.id, newClassification);
                _showSuccessMessage(
                    'Call re-classified as personal. Lead kept.');
              },
              child: const Text('Keep Lead'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Delete lead and reclassify call
                leadProvider.deleteLead(existingLead.id);
                callLogProvider.classifyCall(callLog.id, newClassification);
                _showSuccessMessage(
                    'Call re-classified as personal. Lead deleted.');
              },
              child: const Text('Delete Lead'),
            ),
          ],
        ),
      );
    } else {
      // No existing lead, just reclassify
      callLogProvider.classifyCall(callLog.id, newClassification);
      _showSuccessMessage('Call re-classified as personal call.');
    }
  }

  void _handleLeadCreated(LeadModel lead, LeadProvider leadProvider) {
    leadProvider.addLeadFromClassification(lead);
    _showSuccessMessage('New lead created from call classification!');
  }

  void _handleLeadUpdated(
      LeadModel lead, LeadStatus newStatus, LeadProvider leadProvider) {
    leadProvider.updateLead(lead);
    _showSuccessMessage(
        'Lead status updated to ${_getStatusDisplayName(newStatus)}!');
  }

  void _showSuccessMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  String _getStatusDisplayName(LeadStatus status) {
    switch (status) {
      case LeadStatus.new_lead:
        return 'New Lead';
      case LeadStatus.contacted:
        return 'Contacted';
      case LeadStatus.qualified:
        return 'Qualified';
      case LeadStatus.proposal:
        return 'Proposal Sent';
      case LeadStatus.negotiation:
        return 'In Negotiation';
      case LeadStatus.closed_won:
        return 'Closed Won';
      case LeadStatus.closed_lost:
        return 'Closed Lost';
      case LeadStatus.follow_up:
        return 'Follow Up';
    }
  }
}

class _EnhancedCallLogCard extends StatelessWidget {
  final CallLogModel callLog;
  final VoidCallback onCall;
  final VoidCallback onSMS;
  final VoidCallback onWhatsApp;
  final VoidCallback onClassify;
  final int index;
  final String searchQuery;

  const _EnhancedCallLogCard({
    required this.callLog,
    required this.onCall,
    required this.onSMS,
    required this.onWhatsApp,
    required this.onClassify,
    required this.index,
    required this.searchQuery,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _showCallLogDetails(context),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Call Type Avatar
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: _getCallTypeColor(callLog.type).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: _getCallTypeColor(callLog.type).withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      _getCallTypeIcon(callLog.type),
                      color: _getCallTypeColor(callLog.type),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Call Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Name and Sync Status
                        Row(
                          children: [
                            Expanded(
                              child: _buildHighlightedText(
                                callLog.name ?? 'Unknown',
                                searchQuery,
                                const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                            if (!callLog.isSynced) _buildSyncBadge(),
                          ],
                        ),
                        const SizedBox(height: 4),

                        // Phone Number
                        _buildHighlightedText(
                          callLog.number,
                          searchQuery,
                          TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Call Type and Duration
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: _getCallTypeColor(callLog.type)
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                _getCallTypeDisplayName(callLog.type),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: _getCallTypeColor(callLog.type),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                _formatDuration(callLog.duration),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Time, Date, and Classification Indicator
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Dynamic classification indicator
                          _buildClassificationBadge(callLog),
                          const SizedBox(width: 8),
                          Text(
                            _formatTime(callLog.timestamp),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatDate(callLog.timestamp),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _getClassificationStatusText(callLog),
                        style: TextStyle(
                          fontSize: 10,
                          color: _getClassificationStatusColor(callLog),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHighlightedText(String text, String query, TextStyle style) {
    if (query.isEmpty) {
      return Text(text, style: style, overflow: TextOverflow.ellipsis);
    }

    final lowerText = text.toLowerCase();
    final lowerQuery = query.toLowerCase();

    if (!lowerText.contains(lowerQuery)) {
      return Text(text, style: style, overflow: TextOverflow.ellipsis);
    }

    final startIndex = lowerText.indexOf(lowerQuery);
    final endIndex = startIndex + query.length;

    return RichText(
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        children: [
          if (startIndex > 0)
            TextSpan(
              text: text.substring(0, startIndex),
              style: style,
            ),
          TextSpan(
            text: text.substring(startIndex, endIndex),
            style: style.copyWith(
              backgroundColor: Colors.yellow.shade200,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (endIndex < text.length)
            TextSpan(
              text: text.substring(endIndex),
              style: style,
            ),
        ],
      ),
    );
  }

  Widget _buildSyncBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange, Colors.orange.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.sync_problem_rounded,
            color: Colors.white,
            size: 12,
          ),
          const SizedBox(width: 4),
          Text(
            'Not synced',
            style: TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _showCallLogDetails(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                children: [
                  Container(
                    width: 64,
                    height: 64,
                    decoration: BoxDecoration(
                      color: _getCallTypeColor(callLog.type)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: _getCallTypeColor(callLog.type)
                            .withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      _getCallTypeIcon(callLog.type),
                      color: _getCallTypeColor(callLog.type),
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          callLog.name ?? 'Unknown',
                          style: const TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          callLog.number,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Action Buttons
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      icon: Icons.call_rounded,
                      label: 'Call',
                      color: Colors.green,
                      onTap: () {
                        Navigator.pop(context);
                        onCall();
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildActionButton(
                      icon: Icons.message_rounded,
                      label: 'SMS',
                      color: Colors.blue,
                      onTap: () {
                        Navigator.pop(context);
                        onSMS();
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildActionButton(
                      icon: Icons.chat_rounded,
                      label: 'WhatsApp',
                      color: Colors.green.shade600,
                      onTap: () {
                        Navigator.pop(context);
                        onWhatsApp();
                      },
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Classification Button
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 24),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    onClassify();
                  },
                  icon: const Icon(Icons.business_center_rounded),
                  label: Text(callLog.classification != null
                      ? 'Re-classify Call'
                      : 'Classify Call'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.shade600,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Call Details
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 24),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  _buildDetailRow(
                      'Call Type', _getCallTypeDisplayName(callLog.type)),
                  _buildDetailRow(
                      'Duration', _formatDuration(callLog.duration)),
                  _buildDetailRow('Date', _formatDetailDate(callLog.timestamp)),
                  _buildDetailRow('Time', _formatTime(callLog.timestamp)),
                  _buildDetailRow('Sync Status',
                      callLog.isSynced ? 'Synced' : 'Not Synced'),
                  _buildDetailRow(
                      'Call Quality', _getCallQuality(callLog.duration)),
                ],
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: color,
                size: 28,
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  String _getCallQuality(int duration) {
    if (duration == 0) return 'No Answer';
    if (duration < 30) return 'Brief';
    if (duration < 120) return 'Short';
    if (duration < 600) return 'Good';
    return 'Extended';
  }

  Color _getCallTypeColor(CallType type) {
    switch (type) {
      case CallType.incoming:
        return Colors.green;
      case CallType.outgoing:
        return Colors.blue;
      case CallType.missed:
        return Colors.red;
      case CallType.rejected:
        return Colors.orange;
    }
  }

  IconData _getCallTypeIcon(CallType type) {
    switch (type) {
      case CallType.incoming:
        return Icons.call_received_rounded;
      case CallType.outgoing:
        return Icons.call_made_rounded;
      case CallType.missed:
        return Icons.call_received_rounded;
      case CallType.rejected:
        return Icons.call_end_rounded;
    }
  }

  String _getCallTypeDisplayName(CallType type) {
    switch (type) {
      case CallType.incoming:
        return 'Incoming';
      case CallType.outgoing:
        return 'Outgoing';
      case CallType.missed:
        return 'Missed';
      case CallType.rejected:
        return 'Rejected';
    }
  }

  String _formatDuration(int seconds) {
    if (seconds == 0) return '0s';
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    if (minutes > 0) {
      return '${minutes}m ${remainingSeconds}s';
    }
    return '${remainingSeconds}s';
  }

  String _formatTime(DateTime dateTime) {
    return DateFormat('HH:mm').format(dateTime);
  }

  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final callDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (callDate == today) {
      return 'Today';
    } else if (callDate == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat('MMM dd').format(dateTime);
    }
  }

  String _formatDetailDate(DateTime dateTime) {
    return DateFormat('EEEE, MMMM dd, yyyy').format(dateTime);
  }

  Widget _buildClassificationBadge(CallLogModel callLog) {
    if (callLog.classification == null) {
      // Unclassified - show orange "tap to classify" indicator
      return Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.orange.shade100,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          Icons.business_center_rounded,
          size: 12,
          color: Colors.orange.shade700,
        ),
      );
    } else if (callLog.classification == CallClassification.personal) {
      // Personal call - green badge with person icon
      return Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.green.shade100,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          Icons.person_rounded,
          size: 12,
          color: Colors.green.shade700,
        ),
      );
    } else {
      // Business call - blue badge with briefcase icon
      return Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.blue.shade100,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          Icons.business_center_rounded,
          size: 12,
          color: Colors.blue.shade700,
        ),
      );
    }
  }

  String _getClassificationStatusText(CallLogModel callLog) {
    if (callLog.classification == null) {
      return 'Tap to classify';
    } else if (callLog.classification == CallClassification.personal) {
      return 'Personal';
    } else {
      return 'Business';
    }
  }

  Color _getClassificationStatusColor(CallLogModel callLog) {
    if (callLog.classification == null) {
      return Colors.orange.shade600;
    } else if (callLog.classification == CallClassification.personal) {
      return Colors.green.shade600;
    } else {
      return Colors.blue.shade600;
    }
  }
}
