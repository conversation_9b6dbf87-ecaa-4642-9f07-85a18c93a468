import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:call_log/call_log.dart' as call_log;
import 'package:permission_handler/permission_handler.dart';
import 'package:calling_agent_app/data/models/call_log_model.dart';
import 'package:calling_agent_app/presentation/providers/gamification_provider.dart';
import '../../data/services/api_service.dart';
import '../../data/services/local_storage_service.dart';

class CallLogProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final LocalStorageService _localStorage = LocalStorageService();

  List<CallLogModel> _callLogs = [];
  List<CallLogModel> _filteredCallLogs = [];
  bool _isLoading = false;
  bool _isSyncing = false;
  CallType? _selectedFilter;

  List<CallLogModel> get callLogs => _callLogs;
  List<CallLogModel> get filteredCallLogs => _filteredCallLogs;
  bool get isLoading => _isLoading;
  bool get isSyncing => _isSyncing;
  CallType? get selectedFilter => _selectedFilter;

  Future<void> loadCallLogs(BuildContext context) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Load from local storage first
      final localCallLogs = await _localStorage.getCallLogs();
      _callLogs = localCallLogs;
      _applyFilter();

      // Then load from device call log
      await _loadDeviceCallLogs();

      // Check for new badges
      final gamificationProvider =
          Provider.of<GamificationProvider>(context, listen: false);
      gamificationProvider.checkForNewBadges(_callLogs);
    } catch (e) {
      debugPrint('Error loading call logs: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _loadDeviceCallLogs() async {
    try {
      // Request permission
      final permission = await Permission.phone.request();
      if (!permission.isGranted) {
        debugPrint('Phone permission not granted');
        return;
      }

      // Get call logs from device
      final Iterable<call_log.CallLogEntry> entries =
          await call_log.CallLog.get();

      // Convert to our model
      final deviceCallLogs = entries.map((entry) {
        return CallLogModel(
          id: entry.timestamp.toString(),
          name: entry.name,
          number: entry.number ?? 'Unknown',
          type: _mapCallType(entry.callType),
          timestamp: DateTime.fromMillisecondsSinceEpoch(entry.timestamp ?? 0),
          duration: entry.duration ?? 0,
          isSynced: false,
          isAnswered: (entry.duration ?? 0) > 0,
        );
      }).toList();

      // Merge with existing call logs
      _mergeCallLogs(deviceCallLogs);

      // Save to local storage
      await _localStorage.saveCallLogs(_callLogs);
    } catch (e) {
      debugPrint('Error loading device call logs: $e');
    }
  }

  void _mergeCallLogs(List<CallLogModel> newCallLogs) {
    final existingIds = _callLogs.map((log) => log.id).toSet();
    final uniqueNewLogs =
        newCallLogs.where((log) => !existingIds.contains(log.id)).toList();

    _callLogs.addAll(uniqueNewLogs);
    _callLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    _applyFilter();
  }

  CallType _mapCallType(call_log.CallType? callType) {
    switch (callType) {
      case call_log.CallType.incoming:
        return CallType.incoming;
      case call_log.CallType.outgoing:
        return CallType.outgoing;
      case call_log.CallType.missed:
        return CallType.missed;
      case call_log.CallType.rejected:
        return CallType.rejected;
      default:
        return CallType.outgoing;
    }
  }

  Future<void> syncCallLogs() async {
    _isSyncing = true;
    notifyListeners();

    try {
      final unsyncedLogs = _callLogs.where((log) => !log.isSynced).toList();

      if (unsyncedLogs.isNotEmpty) {
        await _apiService.syncCallLogs(unsyncedLogs);

        // Mark as synced
        for (var i = 0; i < _callLogs.length; i++) {
          if (!_callLogs[i].isSynced) {
            _callLogs[i] = _callLogs[i].copyWith(isSynced: true);
          }
        }

        await _localStorage.saveCallLogs(_callLogs);
        _applyFilter();
      }
    } catch (e) {
      debugPrint('Error syncing call logs: $e');
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  void filterByType(CallType? type) {
    _selectedFilter = type;
    _applyFilter();
    notifyListeners();
  }

  void _applyFilter() {
    if (_selectedFilter == null) {
      _filteredCallLogs = List.from(_callLogs);
    } else {
      _filteredCallLogs =
          _callLogs.where((log) => log.type == _selectedFilter).toList();
    }
  }

  List<CallLogModel> getRecentCalls(int limit) {
    return _callLogs.take(limit).toList();
  }

  Map<String, dynamic> getTodayStats() {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);

    final todayCalls = _callLogs
        .where((log) =>
            log.timestamp.isAfter(todayStart) &&
            log.timestamp.isBefore(todayStart.add(const Duration(days: 1))))
        .toList();

    final totalCalls = todayCalls.length;
    final totalDuration =
        todayCalls.fold<int>(0, (sum, log) => sum + log.duration);
    final outgoingCalls =
        todayCalls.where((log) => log.type == CallType.outgoing).length;
    final incomingCalls =
        todayCalls.where((log) => log.type == CallType.incoming).length;
    final missedCalls =
        todayCalls.where((log) => log.type == CallType.missed).length;

    return {
      'totalCalls': totalCalls,
      'totalDuration': (totalDuration / 60).round(), // in minutes
      'outgoingCalls': outgoingCalls,
      'incomingCalls': incomingCalls,
      'missedCalls': missedCalls,
    };
  }

  Map<String, dynamic> getAdvancedStats({int days = 7}) {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    final filteredLogs = _callLogs
        .where((log) =>
            log.timestamp.isAfter(startDate) && log.timestamp.isBefore(endDate))
        .toList();

    final totalCalls = filteredLogs.length;
    final totalTalkTime =
        filteredLogs.fold<int>(0, (sum, log) => sum + log.duration);
    final avgCallDuration = totalCalls > 0 ? totalTalkTime / totalCalls : 0.0;

    final outgoingCalls =
        filteredLogs.where((log) => log.type == CallType.outgoing).toList();
    final answeredOutgoingCalls =
        outgoingCalls.where((log) => log.isAnswered).toList();
    final contactRatio = outgoingCalls.isNotEmpty
        ? answeredOutgoingCalls.length / outgoingCalls.length
        : 0.0;

    final talkRatio =
        totalTalkTime > 0 ? totalTalkTime / (outgoingCalls.length * 60) : 0.0;

    final longCalls = filteredLogs.where((log) => log.duration > 60).length;
    final missedCalls =
        filteredLogs.where((log) => log.type == CallType.missed).length;
    final answeredCalls = filteredLogs.where((log) => log.isAnswered).length;

    // Calculate growth compared to previous period
    final previousPeriodStart = startDate.subtract(Duration(days: days));
    final previousPeriodLogs = _callLogs
        .where((log) =>
            log.timestamp.isAfter(previousPeriodStart) &&
            log.timestamp.isBefore(startDate))
        .toList();

    final previousTotalCalls = previousPeriodLogs.length;
    final callsGrowth = previousTotalCalls > 0
        ? ((totalCalls - previousTotalCalls) / previousTotalCalls * 100).round()
        : 0;

    // Calculate conversion rate (assuming you have a way to track conversions)
    final conversionRate = _calculateConversionRate(filteredLogs);

    return {
      'totalCalls': totalCalls,
      'totalTalkTime': (totalTalkTime / 60).round(), // in minutes
      'avgCallDuration': avgCallDuration.round(), // in seconds
      'contactRatio': contactRatio,
      'talkRatio': talkRatio,
      'longCalls': longCalls,
      'missedCalls': missedCalls,
      'answeredCalls': answeredCalls,
      'callsGrowth': callsGrowth,
      'conversionRate': conversionRate,
    };
  }

  // New method: Get call type distribution for pie chart
  Map<String, int> getCallTypeDistribution({int days = 7}) {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    final filteredLogs = _callLogs
        .where((log) =>
            log.timestamp.isAfter(startDate) && log.timestamp.isBefore(endDate))
        .toList();

    final distribution = <String, int>{
      'incoming': 0,
      'outgoing': 0,
      'missed': 0,
      'rejected': 0,
    };

    for (final log in filteredLogs) {
      switch (log.type) {
        case CallType.incoming:
          distribution['incoming'] = distribution['incoming']! + 1;
          break;
        case CallType.outgoing:
          distribution['outgoing'] = distribution['outgoing']! + 1;
          break;
        case CallType.missed:
          distribution['missed'] = distribution['missed']! + 1;
          break;
        case CallType.rejected:
          distribution['rejected'] = distribution['rejected']! + 1;
          break;
      }
    }

    return distribution;
  }

  // Additional analytics methods
  Map<String, dynamic> getCallEfficiencyMetrics({int days = 7}) {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    final filteredLogs = _callLogs
        .where((log) =>
            log.timestamp.isAfter(startDate) && log.timestamp.isBefore(endDate))
        .toList();

    final outgoingCalls =
        filteredLogs.where((log) => log.type == CallType.outgoing).toList();
    final answeredOutgoingCalls =
        outgoingCalls.where((log) => log.isAnswered).toList();
    final missedCalls =
        filteredLogs.where((log) => log.type == CallType.missed).toList();
    final longCalls = filteredLogs.where((log) => log.duration > 60).toList();
    final shortCalls = filteredLogs
        .where((log) => log.duration < 10 && log.duration > 0)
        .toList();

    final totalDuration =
        filteredLogs.fold<int>(0, (sum, log) => sum + log.duration);
    final averageDuration =
        filteredLogs.isNotEmpty ? totalDuration / filteredLogs.length : 0.0;

    return {
      'outgoingSuccessRate': outgoingCalls.isNotEmpty
          ? (answeredOutgoingCalls.length / outgoingCalls.length * 100).round()
          : 0,
      'meaningfulConversationRate': filteredLogs.isNotEmpty
          ? (longCalls.length / filteredLogs.length * 100).round()
          : 0,
      'quickCallRate': filteredLogs.isNotEmpty
          ? (shortCalls.length / filteredLogs.length * 100).round()
          : 0,
      'averageCallDuration': averageDuration.round(),
      'totalCallTime': (totalDuration / 60).round(), // in minutes
      'callsPerDay':
          filteredLogs.isNotEmpty ? (filteredLogs.length / days).round() : 0,
    };
  }

  Map<String, dynamic> getHourlyCallDistribution({int days = 7}) {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    final filteredLogs = _callLogs
        .where((log) =>
            log.timestamp.isAfter(startDate) && log.timestamp.isBefore(endDate))
        .toList();

    final hourlyDistribution = <int, int>{};

    // Initialize all hours
    for (int hour = 0; hour < 24; hour++) {
      hourlyDistribution[hour] = 0;
    }

    // Count calls by hour
    for (final log in filteredLogs) {
      final hour = log.timestamp.hour;
      hourlyDistribution[hour] = hourlyDistribution[hour]! + 1;
    }

    // Find peak hour
    final peakHour = hourlyDistribution.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return {
      'hourlyDistribution': hourlyDistribution,
      'peakHour': peakHour,
      'peakHourCalls': hourlyDistribution[peakHour],
    };
  }

  List<Map<String, dynamic>> getWeeklyCallDistribution({int weeks = 4}) {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: weeks * 7));

    final filteredLogs = _callLogs
        .where((log) =>
            log.timestamp.isAfter(startDate) && log.timestamp.isBefore(endDate))
        .toList();

    final weeklyDistribution = <String, int>{
      'Monday': 0,
      'Tuesday': 0,
      'Wednesday': 0,
      'Thursday': 0,
      'Friday': 0,
      'Saturday': 0,
      'Sunday': 0,
    };

    final weekdays = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];

    for (final log in filteredLogs) {
      final weekday = weekdays[log.timestamp.weekday - 1];
      weeklyDistribution[weekday] = weeklyDistribution[weekday]! + 1;
    }

    return weeklyDistribution.entries
        .map((entry) => {
              'day': entry.key,
              'calls': entry.value,
              'percentage': filteredLogs.isNotEmpty
                  ? (entry.value / filteredLogs.length * 100).round()
                  : 0,
            })
        .toList();
  }

  List<Map<String, dynamic>> getDailyTrends({int days = 7}) {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    final dailyTrends = <String, Map<String, int>>{};

    for (var i = 0; i < days; i++) {
      final date = startDate.add(Duration(days: i));
      final dateString = '${date.month}/${date.day}';
      dailyTrends[dateString] = {'calls': 0, 'duration': 0};
    }

    for (final log in _callLogs) {
      if (log.timestamp.isAfter(startDate) && log.timestamp.isBefore(endDate)) {
        final dateString = '${log.timestamp.month}/${log.timestamp.day}';
        if (dailyTrends.containsKey(dateString)) {
          dailyTrends[dateString]!['calls'] =
              dailyTrends[dateString]!['calls']! + 1;
          dailyTrends[dateString]!['duration'] =
              dailyTrends[dateString]!['duration']! + log.duration;
        }
      }
    }

    return dailyTrends.entries
        .map((entry) => {
              'day': entry.key,
              'calls': entry.value['calls'],
              'duration': (entry.value['duration']! / 60).round(), // in minutes
            })
        .toList();
  }

  // Helper method to calculate conversion rate
  double _calculateConversionRate(List<CallLogModel> calls) {
    // This is a placeholder - you'll need to implement based on your business logic
    // For example, you might track conversions in a separate service or database
    // For now, let's assume meaningful calls (>60 seconds) have a 15% conversion rate
    final meaningfulCalls = calls.where((call) => call.duration > 60).length;
    final estimatedConversions = (meaningfulCalls * 0.15).round();
    return calls.isNotEmpty ? (estimatedConversions / calls.length * 100) : 0.0;
  }

  // Classification management methods
  Future<void> classifyCall(
      String callId, CallClassification classification) async {
    try {
      final callIndex = _callLogs.indexWhere((log) => log.id == callId);
      if (callIndex != -1) {
        final updatedCall = _callLogs[callIndex].copyWith(
          classification: classification,
          classificationDate: DateTime.now(),
        );

        _callLogs[callIndex] = updatedCall;
        _applyFilter();

        // Save to local storage
        await _localStorage.saveCallLogs(_callLogs);

        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error classifying call: $e');
    }
  }

  Future<void> reclassifyCall(
      String callId, CallClassification newClassification) async {
    await classifyCall(callId, newClassification);
  }

  CallClassification? getCallClassification(String callId) {
    try {
      final call = _callLogs.firstWhere((log) => log.id == callId);
      return call.classification;
    } catch (e) {
      return null;
    }
  }

  bool isCallClassified(String callId) {
    return getCallClassification(callId) != null;
  }

  List<CallLogModel> getClassifiedCalls(CallClassification classification) {
    return _callLogs
        .where((log) => log.classification == classification)
        .toList();
  }

  Map<String, int> getClassificationStats() {
    final personal = _callLogs
        .where((log) => log.classification == CallClassification.personal)
        .length;
    final business = _callLogs
        .where((log) => log.classification == CallClassification.business)
        .length;
    final unclassified =
        _callLogs.where((log) => log.classification == null).length;

    return {
      'personal': personal,
      'business': business,
      'unclassified': unclassified,
      'total': _callLogs.length,
    };
  }
}
