# 🚀 Calling Agent Backend API Development - Comprehensive Prompt

## 📋 **Project Overview**

You are tasked with developing a complete backend API system for the "Calling Agent" mobile application - a professional call management and lead generation platform. The Flutter mobile app currently uses local Hive storage and needs a robust server-side API for production deployment with multi-user support and web dashboard functionality.

## 🎯 **Core Business Requirements**

### **Business Model:**
- **Channel Partners** (Admins): Companies that generate leads and assign them to agents
- **Agents** (Users): Sales representatives who receive leads and manage calls
- **Lead Flow**: Channel Partners → Create/Upload Leads → Assign to Agents → Agents Work Leads → Report Back

### **Key Workflows:**
1. **Lead Distribution**: Channel partner uploads leads → assigns to specific agents → agents receive on mobile
2. **Call-to-Lead**: Agent classifies phone calls as business → creates leads → syncs to server → visible to channel partner
3. **Call Analytics**: Call logs from agent phones → sync to server → analytics for channel partners
4. **Status Tracking**: Lead status updates from mobile → real-time dashboard updates

## 🏗️ **Technical Architecture Requirements**

### **Technology Stack:**
- **Backend Framework**: Node.js with Express.js OR Python with FastAPI/Django
- **Database**: PostgreSQL with Redis for caching
- **Authentication**: JWT tokens with refresh token mechanism
- **File Storage**: AWS S3 or similar for CSV uploads and attachments
- **Real-time**: WebSocket support for live dashboard updates
- **Documentation**: Swagger/OpenAPI 3.0 specification
- **Deployment**: Docker containers with environment-based configuration

### **API Design Principles:**
- RESTful architecture with proper HTTP methods and status codes
- Consistent JSON response format with error handling
- Pagination for list endpoints (limit/offset or cursor-based)
- Rate limiting and request validation
- CORS support for web dashboard
- API versioning (v1, v2) for future compatibility

## 📊 **Database Schema Design**

### **Core Entities:**

```sql
-- Users Table (Channel Partners & Agents)
users:
  - id (UUID, Primary Key)
  - email (String, Unique, Not Null)
  - password_hash (String, Not Null)
  - role (Enum: 'channel_partner', 'agent')
  - first_name (String)
  - last_name (String)
  - company_name (String, for channel partners)
  - phone (String)
  - is_active (Boolean, Default: true)
  - created_at (Timestamp)
  - updated_at (Timestamp)
  - last_login (Timestamp)

-- Channel Partner - Agent Relationships
channel_agent_assignments:
  - id (UUID, Primary Key)
  - channel_partner_id (UUID, Foreign Key → users.id)
  - agent_id (UUID, Foreign Key → users.id)
  - assigned_at (Timestamp)
  - is_active (Boolean, Default: true)

-- Leads Table
leads:
  - id (UUID, Primary Key)
  - channel_partner_id (UUID, Foreign Key → users.id)
  - assigned_agent_id (UUID, Foreign Key → users.id, Nullable)
  - name (String, Not Null)
  - phone (String, Not Null)
  - email (String, Nullable)
  - company (String, Nullable)
  - status (Enum: 'new', 'contacted', 'qualified', 'converted', 'lost')
  - source (Enum: 'upload', 'call_classification', 'manual', 'import')
  - notes (Text, Nullable)
  - estimated_value (Decimal, Nullable)
  - follow_up_date (Date, Nullable)
  - created_at (Timestamp)
  - updated_at (Timestamp)
  - assigned_at (Timestamp, Nullable)
  - last_contacted (Timestamp, Nullable)

-- Call Logs Table
call_logs:
  - id (UUID, Primary Key)
  - agent_id (UUID, Foreign Key → users.id)
  - phone_number (String, Not Null)
  - contact_name (String, Nullable)
  - call_type (Enum: 'incoming', 'outgoing', 'missed')
  - duration (Integer, seconds)
  - call_date (Timestamp)
  - classification (Enum: 'personal', 'business', 'unclassified')
  - lead_id (UUID, Foreign Key → leads.id, Nullable)
  - synced_at (Timestamp)
  - created_at (Timestamp)

-- Lead Status History
lead_status_history:
  - id (UUID, Primary Key)
  - lead_id (UUID, Foreign Key → leads.id)
  - previous_status (String)
  - new_status (String)
  - changed_by (UUID, Foreign Key → users.id)
  - notes (Text, Nullable)
  - changed_at (Timestamp)

-- Bulk Upload Jobs
bulk_upload_jobs:
  - id (UUID, Primary Key)
  - channel_partner_id (UUID, Foreign Key → users.id)
  - filename (String)
  - total_records (Integer)
  - processed_records (Integer)
  - successful_records (Integer)
  - failed_records (Integer)
  - status (Enum: 'pending', 'processing', 'completed', 'failed')
  - error_log (JSON, Nullable)
  - created_at (Timestamp)
  - completed_at (Timestamp, Nullable)
```

## 🔐 **Authentication & Authorization**

### **JWT Token Structure:**
```json
{
  "user_id": "uuid",
  "email": "<EMAIL>",
  "role": "channel_partner|agent",
  "company_id": "uuid",
  "exp": 1234567890,
  "iat": 1234567890
}
```

### **Role-Based Access Control:**
- **Channel Partners**: Full access to their leads, agents, and analytics
- **Agents**: Access only to assigned leads and their own call logs
- **Cross-tenant isolation**: Users can only access data within their organization

### **Security Requirements:**
- Password hashing with bcrypt (minimum 12 rounds)
- JWT access tokens (15-minute expiry) + refresh tokens (7-day expiry)
- Rate limiting: 100 requests/minute per user
- Input validation and sanitization
- SQL injection prevention
- HTTPS enforcement in production

## 📡 **API Endpoints Specification**

### **Authentication Endpoints:**
```
POST /api/v1/auth/login
POST /api/v1/auth/refresh
POST /api/v1/auth/logout
POST /api/v1/auth/forgot-password
POST /api/v1/auth/reset-password
GET  /api/v1/auth/me
```

### **User Management:**
```
GET    /api/v1/users                    # List users (channel partners only)
POST   /api/v1/users                    # Create agent (channel partners only)
GET    /api/v1/users/{id}               # Get user details
PUT    /api/v1/users/{id}               # Update user
DELETE /api/v1/users/{id}               # Deactivate user
GET    /api/v1/users/agents             # List assigned agents
POST   /api/v1/users/agents/{id}/assign # Assign agent to channel partner
```

### **Lead Management:**
```
GET    /api/v1/leads                    # List leads (with filters)
POST   /api/v1/leads                    # Create lead
GET    /api/v1/leads/{id}               # Get lead details
PUT    /api/v1/leads/{id}               # Update lead
DELETE /api/v1/leads/{id}               # Delete lead
POST   /api/v1/leads/{id}/assign        # Assign lead to agent
PUT    /api/v1/leads/{id}/status        # Update lead status
GET    /api/v1/leads/{id}/history       # Get lead status history
POST   /api/v1/leads/bulk-upload        # Bulk upload leads (CSV)
GET    /api/v1/leads/bulk-jobs          # List bulk upload jobs
GET    /api/v1/leads/bulk-jobs/{id}     # Get bulk job status
```

### **Call Log Management:**
```
GET    /api/v1/call-logs               # List call logs
POST   /api/v1/call-logs               # Create call log
POST   /api/v1/call-logs/sync          # Bulk sync call logs
PUT    /api/v1/call-logs/{id}/classify # Classify call
POST   /api/v1/call-logs/{id}/create-lead # Create lead from call
GET    /api/v1/call-logs/analytics     # Call analytics
```

### **Dashboard & Analytics:**
```
GET    /api/v1/dashboard/stats         # Dashboard statistics
GET    /api/v1/analytics/leads         # Lead analytics
GET    /api/v1/analytics/calls         # Call analytics
GET    /api/v1/analytics/agents        # Agent performance
GET    /api/v1/reports/leads           # Lead reports (CSV export)
GET    /api/v1/reports/calls           # Call reports (CSV export)
```

## 📱 **Mobile App Integration Requirements**

### **Sync Mechanisms:**
1. **Initial Sync**: Download all assigned leads when app starts
2. **Incremental Sync**: Sync only changes since last sync timestamp
3. **Conflict Resolution**: Server wins for lead assignments, mobile wins for status updates
4. **Offline Support**: Queue operations when offline, sync when online

### **Mobile-Specific Endpoints:**
```
GET    /api/v1/mobile/sync/leads       # Get leads for agent (with timestamp)
POST   /api/v1/mobile/sync/call-logs   # Bulk upload call logs
GET    /api/v1/mobile/sync/status      # Check sync status
POST   /api/v1/mobile/sync/conflicts   # Resolve sync conflicts
```

### **Push Notifications:**
- New lead assignments
- Lead status updates from channel partner
- System announcements
- Integration with FCM (Firebase Cloud Messaging)

## 🌐 **Web Dashboard Requirements**

### **Channel Partner Dashboard Features:**
1. **Lead Management**: Create, assign, monitor leads
2. **Agent Management**: Add agents, view performance
3. **Analytics**: Lead conversion rates, call statistics
4. **Bulk Operations**: CSV upload, mass assignment
5. **Reports**: Exportable reports in CSV/PDF format

### **Real-time Updates:**
- WebSocket connection for live dashboard updates
- Lead status changes from mobile app
- Agent activity indicators
- New call log entries

## 📋 **Implementation Guidelines**

### **Error Handling:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "phone": ["Phone number is required"],
      "email": ["Invalid email format"]
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### **Success Response Format:**
```json
{
  "success": true,
  "data": { /* response data */ },
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### **Validation Rules:**
- Email: Valid email format, unique per system
- Phone: E.164 format preferred, required for leads
- Passwords: Minimum 8 characters, mixed case, numbers
- Lead status: Only valid transitions allowed
- File uploads: CSV only, maximum 10MB, validate headers

### **Performance Requirements:**
- API response time: < 200ms for simple queries
- Bulk operations: Handle up to 10,000 records
- Concurrent users: Support 1,000+ simultaneous users
- Database indexing on frequently queried fields
- Caching for dashboard statistics (5-minute TTL)

## 🚀 **Deployment & DevOps**

### **Environment Configuration:**
- Development, Staging, Production environments
- Environment variables for sensitive data
- Database migrations with rollback capability
- Health check endpoints for monitoring
- Logging with structured format (JSON)

### **Monitoring & Observability:**
- API endpoint monitoring
- Database performance metrics
- Error tracking and alerting
- User activity logging
- System resource monitoring

## 📚 **Documentation Requirements**

### **API Documentation:**
- Complete OpenAPI 3.0 specification
- Interactive Swagger UI
- Code examples in multiple languages
- Authentication flow documentation
- Error code reference

### **Integration Guide:**
- Mobile app integration steps
- Web dashboard setup
- Authentication implementation
- Sync mechanism explanation
- Troubleshooting guide

## 🧪 **Testing Requirements**

### **Test Coverage:**
- Unit tests for all business logic (>90% coverage)
- Integration tests for API endpoints
- Database transaction tests
- Authentication and authorization tests
- Performance tests for bulk operations

### **Test Data:**
- Sample users (channel partners and agents)
- Test leads with various statuses
- Mock call log data
- CSV files for bulk upload testing

## 🔄 **Migration Strategy**

### **Data Migration from Mobile App:**
- Export existing Hive data to JSON format
- Import leads and call logs to server
- Maintain data integrity during migration
- Provide migration tools for users

### **Backward Compatibility:**
- Support both local and server storage during transition
- Graceful fallback to local storage if server unavailable
- Version compatibility checks

---

**Deliverables Expected:**
1. Complete backend API implementation
2. Database schema with migrations
3. API documentation (Swagger)
4. Docker deployment configuration
5. Integration examples for Flutter app
6. Testing suite with sample data
7. Deployment and setup instructions

**Success Criteria:**
- All API endpoints functional and tested
- Mobile app can sync data successfully
- Web dashboard displays real-time data
- System handles concurrent users efficiently
- Security requirements fully implemented
- Documentation is complete and clear

## 📋 **Detailed API Request/Response Examples**

### **Authentication Examples:**

**POST /api/v1/auth/login**
```json
Request:
{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}

Response:
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 900,
    "user": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "email": "<EMAIL>",
      "role": "channel_partner",
      "first_name": "John",
      "last_name": "Doe",
      "company_name": "ABC Corp"
    }
  }
}
```

### **Lead Management Examples:**

**GET /api/v1/leads?status=new&assigned_agent_id=null&page=1&limit=20**
```json
Response:
{
  "success": true,
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174001",
      "name": "Jane Smith",
      "phone": "+1234567890",
      "email": "<EMAIL>",
      "company": "XYZ Inc",
      "status": "new",
      "source": "upload",
      "estimated_value": 5000.00,
      "created_at": "2024-01-01T10:00:00Z",
      "assigned_agent": null
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "pages": 8
    }
  }
}
```

**POST /api/v1/leads**
```json
Request:
{
  "name": "John Customer",
  "phone": "+1987654321",
  "email": "<EMAIL>",
  "company": "Customer Corp",
  "source": "manual",
  "estimated_value": 10000.00,
  "notes": "Interested in premium package"
}

Response:
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174002",
    "name": "John Customer",
    "phone": "+1987654321",
    "email": "<EMAIL>",
    "company": "Customer Corp",
    "status": "new",
    "source": "manual",
    "estimated_value": 10000.00,
    "notes": "Interested in premium package",
    "created_at": "2024-01-01T11:00:00Z",
    "channel_partner_id": "123e4567-e89b-12d3-a456-426614174000"
  }
}
```

### **Call Log Sync Example:**

**POST /api/v1/call-logs/sync**
```json
Request:
{
  "call_logs": [
    {
      "phone_number": "+1234567890",
      "contact_name": "Jane Smith",
      "call_type": "outgoing",
      "duration": 180,
      "call_date": "2024-01-01T09:30:00Z",
      "classification": "business"
    },
    {
      "phone_number": "+1987654321",
      "contact_name": "John Customer",
      "call_type": "incoming",
      "duration": 240,
      "call_date": "2024-01-01T10:15:00Z",
      "classification": "business"
    }
  ]
}

Response:
{
  "success": true,
  "data": {
    "synced_count": 2,
    "failed_count": 0,
    "call_logs": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174003",
        "phone_number": "+1234567890",
        "status": "synced"
      },
      {
        "id": "123e4567-e89b-12d3-a456-426614174004",
        "phone_number": "+1987654321",
        "status": "synced"
      }
    ]
  }
}
```

## 🔧 **Advanced Implementation Requirements**

### **Database Optimization:**
```sql
-- Essential Indexes
CREATE INDEX idx_leads_channel_partner ON leads(channel_partner_id);
CREATE INDEX idx_leads_assigned_agent ON leads(assigned_agent_id);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_created_at ON leads(created_at);
CREATE INDEX idx_call_logs_agent ON call_logs(agent_id);
CREATE INDEX idx_call_logs_date ON call_logs(call_date);
CREATE INDEX idx_call_logs_phone ON call_logs(phone_number);

-- Composite Indexes
CREATE INDEX idx_leads_agent_status ON leads(assigned_agent_id, status);
CREATE INDEX idx_call_logs_agent_date ON call_logs(agent_id, call_date);
```

### **Caching Strategy:**
- **Redis Keys**: `user:{user_id}`, `leads:{agent_id}`, `stats:{channel_partner_id}`
- **TTL**: User sessions (7 days), Dashboard stats (5 minutes), Lead lists (1 minute)
- **Cache Invalidation**: On lead status updates, new assignments, user changes

### **File Upload Handling:**
```javascript
// CSV Upload Processing
const multer = require('multer');
const csv = require('csv-parser');

const upload = multer({
  dest: 'uploads/',
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv') {
      cb(null, true);
    } else {
      cb(new Error('Only CSV files allowed'));
    }
  }
});

// Expected CSV Format:
// name,phone,email,company,estimated_value,notes
// "John Doe","+1234567890","<EMAIL>","ABC Corp","5000","Interested in product"
```

### **WebSocket Implementation:**
```javascript
// Real-time Events
const io = require('socket.io')(server);

// Event Types:
// - lead_assigned: New lead assigned to agent
// - lead_status_updated: Lead status changed
// - agent_activity: Agent online/offline status
// - bulk_upload_progress: CSV upload progress

io.on('connection', (socket) => {
  socket.on('join_channel_partner', (channelPartnerId) => {
    socket.join(`channel_${channelPartnerId}`);
  });

  socket.on('join_agent', (agentId) => {
    socket.join(`agent_${agentId}`);
  });
});

// Emit events
io.to(`channel_${channelPartnerId}`).emit('lead_status_updated', leadData);
io.to(`agent_${agentId}`).emit('lead_assigned', leadData);
```

## 📊 **Analytics & Reporting Specifications**

### **Dashboard Metrics:**
```json
{
  "lead_stats": {
    "total_leads": 1250,
    "new_leads": 45,
    "contacted_leads": 320,
    "qualified_leads": 180,
    "converted_leads": 95,
    "conversion_rate": 7.6
  },
  "call_stats": {
    "total_calls": 2840,
    "business_calls": 1420,
    "personal_calls": 1420,
    "average_duration": 185,
    "calls_today": 67
  },
  "agent_performance": [
    {
      "agent_id": "uuid",
      "name": "Agent Name",
      "assigned_leads": 25,
      "converted_leads": 5,
      "conversion_rate": 20.0,
      "total_calls": 150,
      "last_activity": "2024-01-01T15:30:00Z"
    }
  ]
}
```

### **Report Generation:**
- **Lead Reports**: CSV export with filters (date range, status, agent)
- **Call Reports**: CSV export with call analytics
- **Agent Performance**: PDF reports with charts and metrics
- **Scheduled Reports**: Daily/weekly email reports to channel partners

## 🔐 **Advanced Security Measures**

### **API Security Headers:**
```javascript
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000');
  next();
});
```

### **Input Validation Schema:**
```javascript
const leadSchema = {
  name: { type: 'string', minLength: 1, maxLength: 100 },
  phone: { type: 'string', pattern: '^\\+[1-9]\\d{1,14}$' },
  email: { type: 'string', format: 'email' },
  estimated_value: { type: 'number', minimum: 0, maximum: 1000000 }
};
```

### **Rate Limiting Configuration:**
```javascript
const rateLimit = require('express-rate-limit');

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many login attempts'
});

const apiLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  message: 'Rate limit exceeded'
});
```

## 🧪 **Testing Framework Requirements**

### **Test Structure:**
```
tests/
├── unit/
│   ├── models/
│   ├── services/
│   └── utils/
├── integration/
│   ├── auth/
│   ├── leads/
│   └── call-logs/
├── e2e/
│   ├── user-flows/
│   └── api-workflows/
└── fixtures/
    ├── users.json
    ├── leads.json
    └── call-logs.json
```

### **Sample Test Cases:**
```javascript
describe('Lead Management API', () => {
  test('should create lead with valid data', async () => {
    const response = await request(app)
      .post('/api/v1/leads')
      .set('Authorization', `Bearer ${token}`)
      .send(validLeadData);

    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
    expect(response.body.data.id).toBeDefined();
  });

  test('should reject lead with invalid phone', async () => {
    const response = await request(app)
      .post('/api/v1/leads')
      .set('Authorization', `Bearer ${token}`)
      .send({ ...validLeadData, phone: 'invalid' });

    expect(response.status).toBe(400);
    expect(response.body.error.code).toBe('VALIDATION_ERROR');
  });
});
```

## 🚀 **Deployment Configuration**

### **Docker Configuration:**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### **Environment Variables:**
```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/calling_agent
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-super-secret-key
JWT_REFRESH_SECRET=your-refresh-secret-key

# File Storage
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=calling-agent-uploads

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Push Notifications
FCM_SERVER_KEY=your-fcm-server-key
```

This comprehensive prompt provides everything needed to develop a production-ready backend API system for your Calling Agent mobile app!
