import 'dart:async';
import 'package:flutter/services.dart';
import '../data/models/call_log_model.dart';

class CallEndDetectionService {
  static const MethodChannel _channel = MethodChannel('call_end_detection');
  static CallEndDetectionService? _instance;
  
  StreamController<CallLogModel>? _callEndController;
  Stream<CallLogModel>? _callEndStream;
  
  CallEndDetectionService._();
  
  static CallEndDetectionService get instance {
    _instance ??= CallEndDetectionService._();
    return _instance!;
  }
  
  Stream<CallLogModel> get callEndStream {
    _callEndStream ??= _createCallEndStream();
    return _callEndStream!;
  }
  
  Stream<CallLogModel> _createCallEndStream() {
    _callEndController = StreamController<CallLogModel>.broadcast();
    _channel.setMethodCallHandler(_handleMethodCall);
    return _callEndController!.stream;
  }
  
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onCallEnded':
        final Map<String, dynamic> callData = Map<String, dynamic>.from(call.arguments);
        final callLog = _parseCallLog(callData);
        _callEndController?.add(callLog);
        break;
    }
  }
  
  CallLogModel _parseCallLog(Map<String, dynamic> data) {
    return CallLogModel(
      id: data['id']?.toString() ?? DateTime.now().millisecondsSinceEpoch.toString(),
      name: data['name'],
      number: data['number'] ?? 'Unknown',
      type: _parseCallType(data['type']),
      timestamp: DateTime.fromMillisecondsSinceEpoch(data['timestamp'] ?? DateTime.now().millisecondsSinceEpoch),
      duration: data['duration'] ?? 0,
      isSynced: false,
      isAnswered: (data['duration'] ?? 0) > 0,
    );
  }
  
  CallType _parseCallType(dynamic type) {
    switch (type?.toString().toLowerCase()) {
      case 'incoming':
        return CallType.incoming;
      case 'outgoing':
        return CallType.outgoing;
      case 'missed':
        return CallType.missed;
      default:
        return CallType.incoming;
    }
  }
  
  Future<void> startListening() async {
    try {
      await _channel.invokeMethod('startListening');
    } on PlatformException catch (e) {
      print('Failed to start call end detection: ${e.message}');
    }
  }
  
  Future<void> stopListening() async {
    try {
      await _channel.invokeMethod('stopListening');
    } on PlatformException catch (e) {
      print('Failed to stop call end detection: ${e.message}');
    }
  }
  
  void dispose() {
    _callEndController?.close();
    _callEndController = null;
    _callEndStream = null;
  }
}
