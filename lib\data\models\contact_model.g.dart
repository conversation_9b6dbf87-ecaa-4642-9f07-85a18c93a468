// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ContactModelAdapter extends TypeAdapter<ContactModel> {
  @override
  final int typeId = 5;

  @override
  ContactModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ContactModel(
      id: fields[0] as String,
      displayName: fields[1] as String,
      givenName: fields[2] as String?,
      familyName: fields[3] as String?,
      phoneNumbers: (fields[4] as List).cast<PhoneNumber>(),
      emailAddresses: (fields[5] as List).cast<EmailAddress>(),
      avatar: fields[6] as Uint8List?,
      isFavorite: fields[7] as bool,
      lastContactedDate: fields[8] as DateTime?,
      contactFrequency: fields[9] as int,
    );
  }

  @override
  void write(BinaryWriter writer, ContactModel obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.displayName)
      ..writeByte(2)
      ..write(obj.givenName)
      ..writeByte(3)
      ..write(obj.familyName)
      ..writeByte(4)
      ..write(obj.phoneNumbers)
      ..writeByte(5)
      ..write(obj.emailAddresses)
      ..writeByte(6)
      ..write(obj.avatar)
      ..writeByte(7)
      ..write(obj.isFavorite)
      ..writeByte(8)
      ..write(obj.lastContactedDate)
      ..writeByte(9)
      ..write(obj.contactFrequency);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ContactModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PhoneNumberAdapter extends TypeAdapter<PhoneNumber> {
  @override
  final int typeId = 6;

  @override
  PhoneNumber read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PhoneNumber(
      number: fields[0] as String,
      label: fields[1] as String,
      isPrimary: fields[2] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, PhoneNumber obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.number)
      ..writeByte(1)
      ..write(obj.label)
      ..writeByte(2)
      ..write(obj.isPrimary);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PhoneNumberAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EmailAddressAdapter extends TypeAdapter<EmailAddress> {
  @override
  final int typeId = 7;

  @override
  EmailAddress read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return EmailAddress(
      address: fields[0] as String,
      label: fields[1] as String,
      isPrimary: fields[2] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, EmailAddress obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.address)
      ..writeByte(1)
      ..write(obj.label)
      ..writeByte(2)
      ..write(obj.isPrimary);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EmailAddressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
