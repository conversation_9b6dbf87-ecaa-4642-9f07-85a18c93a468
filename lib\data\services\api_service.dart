import 'package:dio/dio.dart';
import 'package:calling_agent_app/data/services/local_storage_service.dart';

import '../../core/constants/api_constants.dart';
import 'package:calling_agent_app/data/models/call_log_model.dart';
import 'package:calling_agent_app/data/models/lead_model.dart';
import 'package:calling_agent_app/data/models/user_model.dart';

class ApiService {
  late final Dio _dio;
  final LocalStorageService _localStorageService = LocalStorageService();

  factory ApiService() {
    return _instance;
  }

  ApiService._internal() {
    _dio = Dio(
      BaseOptions(
        baseUrl: ApiConstants.baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final token = await _localStorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          return handler.next(options);
        },
        onError: (error, handler) {
          if (error.response?.statusCode == 401) {
            _handleUnauthorized();
          }
          return handler.next(error);
        },
      ),
    );
  }

  static final ApiService _instance = ApiService._internal();

  Future<String?> _getAuthToken() async {
    return _localStorageService.getAuthToken();
  }

  void _handleUnauthorized() {
    _localStorageService.clearUser();
  }

  // Auth endpoints
  Future<UserModel> login(String email, String password) async {
    try {
      final response = await _dio.post('/auth/login', data: {
        'email': email,
        'password': password,
      });

      final user = UserModel.fromJson(response.data['user']);
      await _localStorageService.saveUser(user);
      await _localStorageService.saveAuthToken(response.data['token']);
      return user;
    } catch (e) {
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  Future<void> logout() async {
    try {
      await _dio.post('/auth/logout');
    } catch (e) {
      throw Exception('Logout failed: ${e.toString()}');
    }
  }

  // Lead endpoints
  Future<List<LeadModel>> getLeads({
    int page = 1,
    int limit = 20,
    String? search,
    LeadStatus? status,
  }) async {
    try {
      final response = await _dio.get('/leads', queryParameters: {
        'page': page,
        'limit': limit,
        if (search != null) 'search': search,
        if (status != null) 'status': status.name,
      });

      return (response.data['leads'] as List)
          .map((json) => LeadModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to get leads: ${e.toString()}');
    }
  }

  Future<LeadModel> updateLead(
      String leadId, Map<String, dynamic> updates) async {
    try {
      final response = await _dio.patch('/leads/$leadId', data: updates);
      return LeadModel.fromJson(response.data['lead']);
    } catch (e) {
      throw Exception('Failed to update lead: ${e.toString()}');
    }
  }

  Future<LeadModel> createLead(LeadModel lead) async {
    try {
      final response = await _dio.post('/leads', data: lead.toJson());
      return LeadModel.fromJson(response.data['lead']);
    } catch (e) {
      throw Exception('Failed to create lead: ${e.toString()}');
    }
  }

  // Call log endpoints
  Future<void> syncCallLogs(List<CallLogModel> callLogs) async {
    try {
      await _dio.post('/call-logs/sync', data: {
        'call_logs': callLogs.map((log) => log.toJson()).toList(),
      });
    } catch (e) {
      throw Exception('Failed to sync call logs: ${e.toString()}');
    }
  }

  // Stats endpoints
  Future<Map<String, dynamic>> getStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final response = await _dio.get('/stats', queryParameters: {
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
      });

      return response.data;
    } catch (e) {
      throw Exception('Failed to get stats: ${e.toString()}');
    }
  }
}
