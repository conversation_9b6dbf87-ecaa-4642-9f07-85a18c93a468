import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/call_log_model.dart';
import '../models/lead_model.dart';
import '../models/user_model.dart';
import '../models/contact_model.dart';

class LocalStorageService {
  static const String _userBoxName = 'user';
  static const String _leadsBoxName = 'leads';
  static const String _callLogsBoxName = 'call_logs';
  static const String _contactsBoxName = 'contacts';
  static const String _speedDialBoxName = 'speed_dial';
  static const String _authTokenKey = 'auth_token';

  // User methods
  Future<void> saveUser(UserModel user) async {
    final box = await Hive.openBox<UserModel>(_userBoxName);
    await box.put(0, user);
  }

  Future<UserModel?> getUser() async {
    final box = await Hive.openBox<UserModel>(_userBoxName);
    return box.get(0);
  }

  Future<void> clearUser() async {
    final box = await Hive.openBox<UserModel>(_userBoxName);
    await box.clear();
    await clearAuthToken();
  }

  // Auth token methods
  Future<void> saveAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_authTokenKey, token);
  }

  Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_authTokenKey);
  }

  Future<void> clearAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_authTokenKey);
  }

  // Lead methods
  Future<void> saveLeads(List<LeadModel> leads) async {
    final box = await Hive.openBox<LeadModel>(_leadsBoxName);
    await box.clear();
    await box.addAll(leads);
  }

  Future<List<LeadModel>> getLeads() async {
    final box = await Hive.openBox<LeadModel>(_leadsBoxName);
    return box.values.toList();
  }

  // Call log methods
  Future<void> saveCallLogs(List<CallLogModel> callLogs) async {
    final box = await Hive.openBox<CallLogModel>(_callLogsBoxName);
    await box.clear();
    await box.addAll(callLogs);
  }

  Future<List<CallLogModel>> getCallLogs() async {
    final box = await Hive.openBox<CallLogModel>(_callLogsBoxName);
    return box.values.toList();
  }

  Future<void> saveCallLog(CallLogModel callLog) async {
    final box = await Hive.openBox<CallLogModel>(_callLogsBoxName);
    await box.add(callLog);
  }

  // Contact methods
  Future<void> saveContacts(List<ContactModel> contacts) async {
    final box = await Hive.openBox<ContactModel>(_contactsBoxName);
    await box.clear();
    await box.addAll(contacts);
  }

  Future<List<ContactModel>> getContacts() async {
    final box = await Hive.openBox<ContactModel>(_contactsBoxName);
    return box.values.toList();
  }

  Future<void> saveContact(ContactModel contact) async {
    final box = await Hive.openBox<ContactModel>(_contactsBoxName);
    await box.put(contact.id, contact);
  }

  Future<void> deleteContact(String contactId) async {
    final box = await Hive.openBox<ContactModel>(_contactsBoxName);
    await box.delete(contactId);
  }

  // Speed dial methods
  Future<void> saveSpeedDialContact(ContactModel contact, int position) async {
    final prefs = await SharedPreferences.getInstance();
    final key = 'speed_dial_$position';
    await prefs.setString(key, contact.id);
  }

  Future<List<ContactModel>> getSpeedDialContacts() async {
    final prefs = await SharedPreferences.getInstance();
    final contacts = <ContactModel>[];

    for (int i = 1; i <= 9; i++) {
      final contactId = prefs.getString('speed_dial_$i');
      if (contactId != null) {
        final box = await Hive.openBox<ContactModel>(_contactsBoxName);
        final contact = box.get(contactId);
        if (contact != null) {
          contacts.add(contact);
        }
      }
    }

    return contacts;
  }

  Future<void> removeSpeedDialContact(int position) async {
    final prefs = await SharedPreferences.getInstance();
    final key = 'speed_dial_$position';
    await prefs.remove(key);
  }
}
