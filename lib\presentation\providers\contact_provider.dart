import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../data/models/contact_model.dart';
import '../../data/services/contact_service.dart';
import '../../data/services/local_storage_service.dart';

class ContactProvider with ChangeNotifier {
  final ContactService _contactService = ContactService();
  final LocalStorageService _localStorage = LocalStorageService();

  List<ContactModel> _contacts = [];
  List<ContactModel> _searchResults = [];
  List<ContactModel> _favoriteContacts = [];
  bool _isLoading = false;
  String _lastSearchQuery = '';

  List<ContactModel> get contacts => _contacts;
  List<ContactModel> get searchResults => _searchResults;
  List<ContactModel> get favoriteContacts => _favoriteContacts;
  bool get isLoading => _isLoading;

  ContactProvider() {
    _loadLocalContacts();
  }

  Future<void> loadContacts() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Load from local storage first
      await _loadLocalContacts();

      // Check permission and load device contacts
      final permission = await Permission.contacts.status;
      if (permission.isGranted) {
        await _loadDeviceContacts();
      } else {
        debugPrint('Contacts permission not granted');
      }
    } catch (e) {
      debugPrint('Error loading contacts: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _loadLocalContacts() async {
    try {
      _contacts = await _localStorage.getContacts();
      _loadFavoriteContacts();
    } catch (e) {
      debugPrint('Error loading local contacts: $e');
    }
  }

  Future<void> _loadDeviceContacts() async {
    try {
      final deviceContacts = await _contactService.getAllContacts();
      
      // Merge with local contacts, avoiding duplicates
      final mergedContacts = <String, ContactModel>{};
      
      // Add local contacts first
      for (final contact in _contacts) {
        mergedContacts[contact.id] = contact;
      }
      
      // Add device contacts, updating existing ones
      for (final contact in deviceContacts) {
        mergedContacts[contact.id] = contact;
      }
      
      _contacts = mergedContacts.values.toList();
      _contacts.sort((a, b) => a.displayName.compareTo(b.displayName));
      
      // Save to local storage
      await _localStorage.saveContacts(_contacts);
    } catch (e) {
      debugPrint('Error loading device contacts: $e');
    }
  }

  void _loadFavoriteContacts() {
    _favoriteContacts = _contacts.where((contact) => contact.isFavorite).toList();
  }

  Future<void> searchContacts(String query) async {
    _lastSearchQuery = query;
    
    if (query.isEmpty) {
      _searchResults.clear();
      notifyListeners();
      return;
    }

    try {
      _searchResults = _contacts.where((contact) {
        final nameMatch = contact.displayName
            .toLowerCase()
            .contains(query.toLowerCase());
        final phoneMatch = contact.phoneNumbers.any((phone) =>
            phone.number.replaceAll(RegExp(r'[^\d]'), '').contains(query));
        final emailMatch = contact.emailAddresses.any((email) =>
            email.address.toLowerCase().contains(query.toLowerCase()));
        
        return nameMatch || phoneMatch || emailMatch;
      }).toList();
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error searching contacts: $e');
    }
  }

  Future<void> addContact(ContactModel contact) async {
    try {
      // Add to device contacts
      final createdContact = await _contactService.createContact(
        displayName: contact.displayName,
        givenName: contact.givenName,
        familyName: contact.familyName,
        phoneNumbers: contact.phoneNumbers,
        emailAddresses: contact.emailAddresses,
      );

      // Add to local list
      _contacts.add(createdContact);
      _contacts.sort((a, b) => a.displayName.compareTo(b.displayName));

      // Save to local storage
      await _localStorage.saveContact(createdContact);

      // Update search results if needed
      if (_lastSearchQuery.isNotEmpty) {
        await searchContacts(_lastSearchQuery);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error adding contact: $e');
      rethrow;
    }
  }

  Future<void> updateContact(ContactModel contact) async {
    try {
      // Update device contact
      final updatedContact = await _contactService.updateContact(contact);

      // Update local list
      final index = _contacts.indexWhere((c) => c.id == contact.id);
      if (index != -1) {
        _contacts[index] = updatedContact;
        _contacts.sort((a, b) => a.displayName.compareTo(b.displayName));
      }

      // Save to local storage
      await _localStorage.saveContact(updatedContact);

      // Update favorites if needed
      _loadFavoriteContacts();

      // Update search results if needed
      if (_lastSearchQuery.isNotEmpty) {
        await searchContacts(_lastSearchQuery);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error updating contact: $e');
      rethrow;
    }
  }

  Future<void> deleteContact(String contactId) async {
    try {
      // Delete from device
      await _contactService.deleteContact(contactId);

      // Remove from local list
      _contacts.removeWhere((contact) => contact.id == contactId);

      // Remove from local storage
      await _localStorage.deleteContact(contactId);

      // Update favorites
      _loadFavoriteContacts();

      // Update search results if needed
      if (_lastSearchQuery.isNotEmpty) {
        await searchContacts(_lastSearchQuery);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting contact: $e');
      rethrow;
    }
  }

  Future<void> toggleFavorite(String contactId) async {
    try {
      final index = _contacts.indexWhere((c) => c.id == contactId);
      if (index != -1) {
        final contact = _contacts[index];
        final updatedContact = contact.copyWith(isFavorite: !contact.isFavorite);
        
        await updateContact(updatedContact);
      }
    } catch (e) {
      debugPrint('Error toggling favorite: $e');
    }
  }

  Future<void> refreshContacts() async {
    await loadContacts();
  }

  Future<void> importDeviceContacts() async {
    try {
      final permission = await Permission.contacts.request();
      if (permission.isGranted) {
        await _loadDeviceContacts();
        notifyListeners();
      } else {
        throw Exception('Contacts permission denied');
      }
    } catch (e) {
      debugPrint('Error importing device contacts: $e');
      rethrow;
    }
  }

  ContactModel? getContactById(String id) {
    try {
      return _contacts.firstWhere((contact) => contact.id == id);
    } catch (e) {
      return null;
    }
  }

  List<ContactModel> getContactsByPhoneNumber(String phoneNumber) {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    return _contacts.where((contact) {
      return contact.phoneNumbers.any((phone) {
        final cleanPhone = phone.number.replaceAll(RegExp(r'[^\d]'), '');
        return cleanPhone == cleanNumber || cleanPhone.endsWith(cleanNumber);
      });
    }).toList();
  }

  Future<void> updateContactFrequency(String contactId) async {
    try {
      final index = _contacts.indexWhere((c) => c.id == contactId);
      if (index != -1) {
        final contact = _contacts[index];
        final updatedContact = contact.copyWith(
          contactFrequency: contact.contactFrequency + 1,
          lastContactedDate: DateTime.now(),
        );
        
        _contacts[index] = updatedContact;
        await _localStorage.saveContact(updatedContact);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating contact frequency: $e');
    }
  }

  List<ContactModel> getFrequentContacts({int limit = 10}) {
    final sortedContacts = List<ContactModel>.from(_contacts);
    sortedContacts.sort((a, b) => b.contactFrequency.compareTo(a.contactFrequency));
    return sortedContacts.take(limit).toList();
  }

  List<ContactModel> getRecentContacts({int limit = 10}) {
    final recentContacts = _contacts
        .where((contact) => contact.lastContactedDate != null)
        .toList();
    recentContacts.sort((a, b) => 
        b.lastContactedDate!.compareTo(a.lastContactedDate!));
    return recentContacts.take(limit).toList();
  }
}
