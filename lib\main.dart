import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'app.dart';
import 'data/models/user_model.dart';
import 'data/models/lead_model.dart';
import 'data/models/call_log_model.dart';
import 'data/models/contact_model.dart';
import 'data/services/notification_service.dart';
import 'presentation/providers/auth_provider.dart';
import 'presentation/providers/call_log_provider.dart';
import 'package:calling_agent_app/presentation/providers/gamification_provider.dart';
import 'presentation/providers/lead_provider.dart';
import 'presentation/providers/dialer_provider.dart';
import 'presentation/providers/contact_provider.dart';
import 'presentation/providers/enhanced_call_provider.dart';
import 'presentation/providers/speed_dial_provider.dart';
import 'data/models/blocked_number_model.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Register adapters
  Hive.registerAdapter(UserModelAdapter());
  Hive.registerAdapter(LeadModelAdapter());
  Hive.registerAdapter(LeadStatusAdapter());
  Hive.registerAdapter(CallLogModelAdapter());
  Hive.registerAdapter(CallTypeAdapter());
  Hive.registerAdapter(CallClassificationAdapter());
  Hive.registerAdapter(ContactModelAdapter());
  Hive.registerAdapter(PhoneNumberAdapter());
  Hive.registerAdapter(EmailAddressAdapter());
  Hive.registerAdapter(BlockedNumberAdapter());

  // Open boxes
  await Hive.openBox<UserModel>('users');
  await Hive.openBox<LeadModel>('leads');
  await Hive.openBox<CallLogModel>('call_logs');
  await Hive.openBox<ContactModel>('contacts');
  await Hive.openBox<BlockedNumber>('blocked_numbers');
  await Hive.openBox('settings');

  // Initialize notifications
  await NotificationService().initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => CallLogProvider()),
        ChangeNotifierProvider(create: (_) => LeadProvider()),
        ChangeNotifierProvider(create: (_) => GamificationProvider()),
        ChangeNotifierProvider(create: (_) => DialerProvider()),
        ChangeNotifierProvider(create: (_) => ContactProvider()),
        ChangeNotifierProvider(create: (_) => EnhancedCallProvider()),
        ChangeNotifierProvider(create: (_) => SpeedDialProvider()),
      ],
      child: const CallingAgentApp(),
    );
  }
}
