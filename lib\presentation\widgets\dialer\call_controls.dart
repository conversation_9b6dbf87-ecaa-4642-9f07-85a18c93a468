import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CallControls extends StatelessWidget {
  final VoidCallback onCall;
  final VoidCallback onDelete;
  final VoidCallback onLongPressDelete;
  final bool hasNumber;
  final Animation<double> pulseAnimation;

  const CallControls({
    super.key,
    required this.onCall,
    required this.onDelete,
    required this.onLongPressDelete,
    required this.hasNumber,
    required this.pulseAnimation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Add Contact Button
          _buildActionButton(
            context: context,
            icon: Icons.person_add,
            onTap: hasNumber ? () => _showAddContactDialog(context) : null,
            backgroundColor: Colors.grey[100],
            iconColor: hasNumber ? Colors.grey[700] : Colors.grey[400],
          ),

          // Call Button
          AnimatedBuilder(
            animation: pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: pulseAnimation.value,
                child: _buildCallButton(context),
              );
            },
          ),

          // Delete Button
          _buildActionButton(
            context: context,
            icon: Icons.backspace_outlined,
            onTap: hasNumber ? onDelete : null,
            onLongPress: hasNumber ? onLongPressDelete : null,
            backgroundColor: Colors.grey[100],
            iconColor: hasNumber ? Colors.grey[700] : Colors.grey[400],
          ),
        ],
      ),
    );
  }

  Widget _buildCallButton(BuildContext context) {
    return Container(
      width: 70,
      height: 70,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: hasNumber
              ? [Colors.green[400]!, Colors.green[600]!]
              : [Colors.grey[300]!, Colors.grey[400]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(35),
        boxShadow: hasNumber
            ? [
                BoxShadow(
                  color: Colors.green.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ]
            : [],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(35),
          onTap: hasNumber ? () => _onCallTap(context) : null,
          child: Icon(
            Icons.call,
            color: Colors.white,
            size: 30,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    Color? backgroundColor,
    Color? iconColor,
  }) {
    return Container(
      width: 55,
      height: 55,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey[100],
        borderRadius: BorderRadius.circular(27.5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(27.5),
          onTap: onTap,
          onLongPress: onLongPress,
          child: Icon(
            icon,
            color: iconColor ?? Colors.grey[700],
            size: 24,
          ),
        ),
      ),
    );
  }

  void _onCallTap(BuildContext context) {
    HapticFeedback.mediumImpact();
    onCall();
  }

  void _showAddContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text('Add Contact'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const TextField(
              decoration: InputDecoration(
                labelText: 'Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
              ),
              readOnly: true,
              controller: TextEditingController(text: 'Current number'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement add contact functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Contact added successfully!'),
                ),
              );
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}
