import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../data/models/lead_model.dart';

class LeadCard extends StatelessWidget {
  final LeadModel lead;
  final VoidCallback? onTap;

  const LeadCard({
    super.key,
    required this.lead,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: _getStatusColor(lead.status),
                    radius: 24,
                    child: Text(
                      lead.name.substring(0, 1).toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          lead.name,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          lead.phone,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                        if (lead.email != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            lead.email!,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey[500],
                                    ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  // Sync status indicator
                  if (!lead.isSynced) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.orange.shade300),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.sync_problem,
                            size: 14,
                            color: Colors.orange.shade700,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Local',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'call':
                          _makeCall(lead.phone);
                          break;
                        case 'sms':
                          _sendSMS(lead.phone);
                          break;
                        case 'whatsapp':
                          _openWhatsApp(lead.phone);
                          break;
                        case 'email':
                          if (lead.email != null) _sendEmail(lead.email!);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'call',
                        child: Row(
                          children: [
                            Icon(Icons.call, size: 20),
                            SizedBox(width: 8),
                            Text('Call'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'sms',
                        child: Row(
                          children: [
                            Icon(Icons.sms, size: 20),
                            SizedBox(width: 8),
                            Text('SMS'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'whatsapp',
                        child: Row(
                          children: [
                            Icon(Icons.chat, size: 20),
                            SizedBox(width: 8),
                            Text('WhatsApp'),
                          ],
                        ),
                      ),
                      if (lead.email != null)
                        const PopupMenuItem(
                          value: 'email',
                          child: Row(
                            children: [
                              Icon(Icons.email, size: 20),
                              SizedBox(width: 8),
                              Text('Email'),
                            ],
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color:
                          _getStatusColor(lead.status).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: _getStatusColor(lead.status)),
                    ),
                    child: Text(
                      _getStatusDisplayName(lead.status),
                      style: TextStyle(
                        color: _getStatusColor(lead.status),
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (lead.followUpDate != null)
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          size: 16,
                          color: _isFollowUpOverdue(lead.followUpDate!)
                              ? Colors.red
                              : Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(lead.followUpDate!),
                          style: TextStyle(
                            color: _isFollowUpOverdue(lead.followUpDate!)
                                ? Colors.red
                                : Colors.grey[600],
                            fontSize: 12,
                            fontWeight: _isFollowUpOverdue(lead.followUpDate!)
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              if (lead.notes != null) ...[
                const SizedBox(height: 8),
                Text(
                  lead.notes!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _makeCall(String phone) {
    launchUrl(Uri.parse('tel:$phone'));
  }

  void _sendSMS(String phone) {
    launchUrl(Uri.parse('sms:$phone'));
  }

  void _openWhatsApp(String phone) {
    launchUrl(Uri.parse('https://wa.me/$phone'));
  }

  void _sendEmail(String email) {
    launchUrl(Uri.parse('mailto:$email'));
  }

  Color _getStatusColor(LeadStatus status) {
    switch (status) {
      case LeadStatus.new_lead:
        return Colors.blue;
      case LeadStatus.contacted:
        return Colors.orange;
      case LeadStatus.qualified:
        return Colors.purple;
      case LeadStatus.proposal:
        return Colors.indigo;
      case LeadStatus.negotiation:
        return Colors.amber;
      case LeadStatus.closed_won:
        return Colors.green;
      case LeadStatus.closed_lost:
        return Colors.red;
      case LeadStatus.follow_up:
        return Colors.teal;
    }
  }

  String _getStatusDisplayName(LeadStatus status) {
    switch (status) {
      case LeadStatus.new_lead:
        return 'New Lead';
      case LeadStatus.contacted:
        return 'Contacted';
      case LeadStatus.qualified:
        return 'Qualified';
      case LeadStatus.proposal:
        return 'Proposal';
      case LeadStatus.negotiation:
        return 'Negotiation';
      case LeadStatus.closed_won:
        return 'Closed Won';
      case LeadStatus.closed_lost:
        return 'Closed Lost';
      case LeadStatus.follow_up:
        return 'Follow-up';
    }
  }

  bool _isFollowUpOverdue(DateTime followUpDate) {
    return followUpDate.isBefore(DateTime.now());
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
