import 'package:flutter/material.dart';
import '../../data/models/call_log_model.dart';
import '../../data/models/lead_model.dart';

class CallClassificationDialog extends StatefulWidget {
  final CallLogModel callLog;
  final Function(bool isBusinessCall) onClassified;
  final Function(LeadModel lead)? onLeadCreated;
  final Function(LeadModel lead, LeadStatus newStatus)? onLeadUpdated;
  final LeadModel? existingLead;
  final CallClassification? currentClassification;

  const CallClassificationDialog({
    super.key,
    required this.callLog,
    required this.onClassified,
    this.onLeadCreated,
    this.onLeadUpdated,
    this.existingLead,
    this.currentClassification,
  });

  @override
  State<CallClassificationDialog> createState() =>
      _CallClassificationDialogState();
}

class _CallClassificationDialogState extends State<CallClassificationDialog> {
  LeadStatus? selectedStatus;
  final TextEditingController notesController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController estimatedValueController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.existingLead != null) {
      selectedStatus = widget.existingLead!.status;
      notesController.text = widget.existingLead!.notes ?? '';
      nameController.text = widget.existingLead!.name;
      estimatedValueController.text =
          widget.existingLead!.estimatedValue?.toString() ?? '';
    } else {
      nameController.text = widget.callLog.name ?? '';
      selectedStatus = LeadStatus.new_lead;
    }
  }

  @override
  void dispose() {
    notesController.dispose();
    nameController.dispose();
    estimatedValueController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade50,
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.phone_callback,
                      color: Colors.blue.shade700,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.currentClassification != null
                                ? 'Re-classify This Call'
                                : 'Classify This Call',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade800,
                            ),
                          ),
                          Text(
                            widget.currentClassification != null
                                ? 'Previously classified as: ${_getClassificationDisplayName(widget.currentClassification!)}'
                                : 'How would you classify this call?',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.blue.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Call Info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getCallTypeIcon(),
                      color: _getCallTypeColor(),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.callLog.name ?? widget.callLog.number,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            '${widget.callLog.type.name.toUpperCase()} • ${_formatDuration(widget.callLog.duration)}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      'Personal Call',
                      Icons.person,
                      Colors.grey.shade600,
                      () => _handlePersonalCall(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildActionButton(
                      'Business Call',
                      Icons.business,
                      Colors.blue.shade600,
                      () => _handleBusinessCall(),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
      String label, IconData icon, Color color, VoidCallback onTap) {
    return Material(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handlePersonalCall() {
    widget.onClassified(false);
    Navigator.of(context).pop();
  }

  void _handleBusinessCall() {
    if (widget.existingLead != null) {
      _showLeadUpdateDialog();
    } else {
      _showLeadCreationDialog();
    }
  }

  void _showLeadCreationDialog() {
    showDialog(
      context: context,
      builder: (context) => _buildLeadDialog(isUpdate: false),
    );
  }

  void _showLeadUpdateDialog() {
    showDialog(
      context: context,
      builder: (context) => _buildLeadDialog(isUpdate: true),
    );
  }

  Widget _buildLeadDialog({required bool isUpdate}) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isUpdate ? 'Update Lead Status' : 'Create New Lead',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              if (!isUpdate) ...[
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Contact Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
              ],
              DropdownButtonFormField<LeadStatus>(
                value: selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Lead Status',
                  border: OutlineInputBorder(),
                ),
                items: LeadStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(_getStatusDisplayName(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedStatus = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: estimatedValueController,
                decoration: const InputDecoration(
                  labelText: 'Estimated Value (\$) - Optional',
                  hintText: 'Enter estimated deal value',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).pop();
                      },
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _saveLead(isUpdate),
                      child: Text(isUpdate ? 'Update' : 'Create'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveLead(bool isUpdate) {
    if (selectedStatus == null) return;

    if (isUpdate && widget.existingLead != null) {
      final updatedLead = widget.existingLead!.copyWith(
        status: selectedStatus,
        notes: notesController.text.isEmpty ? null : notesController.text,
        estimatedValue: double.tryParse(estimatedValueController.text),
        updatedAt: DateTime.now(),
      );
      widget.onLeadUpdated?.call(updatedLead, selectedStatus!);
    } else {
      final newLead = LeadModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: nameController.text.isEmpty
            ? 'Unknown Contact'
            : nameController.text,
        phone: widget.callLog.number,
        status: selectedStatus!,
        notes: notesController.text.isEmpty ? null : notesController.text,
        estimatedValue: double.tryParse(estimatedValueController.text),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        source: 'Call Classification',
      );
      widget.onLeadCreated?.call(newLead);
    }

    widget.onClassified(true);
    Navigator.of(context).pop();
    Navigator.of(context).pop();
  }

  IconData _getCallTypeIcon() {
    switch (widget.callLog.type) {
      case CallType.incoming:
        return Icons.call_received;
      case CallType.outgoing:
        return Icons.call_made;
      case CallType.missed:
        return Icons.call_received;
      default:
        return Icons.phone;
    }
  }

  Color _getCallTypeColor() {
    switch (widget.callLog.type) {
      case CallType.incoming:
        return Colors.green;
      case CallType.outgoing:
        return Colors.blue;
      case CallType.missed:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDuration(int seconds) {
    if (seconds == 0) return 'Not answered';
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes}m ${remainingSeconds}s';
  }

  String _getStatusDisplayName(LeadStatus status) {
    switch (status) {
      case LeadStatus.new_lead:
        return 'New Lead';
      case LeadStatus.contacted:
        return 'Contacted';
      case LeadStatus.qualified:
        return 'Qualified';
      case LeadStatus.proposal:
        return 'Proposal Sent';
      case LeadStatus.negotiation:
        return 'In Negotiation';
      case LeadStatus.closed_won:
        return 'Closed Won';
      case LeadStatus.closed_lost:
        return 'Closed Lost';
      case LeadStatus.follow_up:
        return 'Follow Up';
    }
  }

  String _getClassificationDisplayName(CallClassification classification) {
    switch (classification) {
      case CallClassification.personal:
        return 'Personal Call';
      case CallClassification.business:
        return 'Business Call';
    }
  }
}
