// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'blocked_number_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BlockedNumberAdapter extends TypeAdapter<BlockedNumber> {
  @override
  final int typeId = 8;

  @override
  BlockedNumber read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BlockedNumber(
      id: fields[0] as String,
      phoneNumber: fields[1] as String,
      originalNumber: fields[2] as String,
      reason: fields[3] as String,
      blockedAt: fields[4] as DateTime,
      blockCount: fields[5] as int,
      lastBlockedCall: fields[6] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, BlockedNumber obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.phoneNumber)
      ..writeByte(2)
      ..write(obj.originalNumber)
      ..writeByte(3)
      ..write(obj.reason)
      ..writeByte(4)
      ..write(obj.blockedAt)
      ..writeByte(5)
      ..write(obj.blockCount)
      ..writeByte(6)
      ..write(obj.lastBlockedCall);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BlockedNumberAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
