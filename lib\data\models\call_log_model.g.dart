// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_log_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CallLogModelAdapter extends TypeAdapter<CallLogModel> {
  @override
  final int typeId = 4;

  @override
  CallLogModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CallLogModel(
      id: fields[0] as String,
      name: fields[1] as String?,
      number: fields[2] as String,
      type: fields[3] as CallType,
      timestamp: fields[4] as DateTime,
      duration: fields[5] as int,
      isSynced: fields[6] as bool,
      leadId: fields[7] as String?,
      isAnswered: fields[8] as bool,
      classification: fields[9] as CallClassification?,
      classificationDate: fields[10] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, CallLogModel obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.number)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.timestamp)
      ..writeByte(5)
      ..write(obj.duration)
      ..writeByte(6)
      ..write(obj.isSynced)
      ..writeByte(7)
      ..write(obj.leadId)
      ..writeByte(8)
      ..write(obj.isAnswered)
      ..writeByte(9)
      ..write(obj.classification)
      ..writeByte(10)
      ..write(obj.classificationDate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallLogModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CallClassificationAdapter extends TypeAdapter<CallClassification> {
  @override
  final int typeId = 3;

  @override
  CallClassification read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CallClassification.personal;
      case 1:
        return CallClassification.business;
      default:
        return CallClassification.personal;
    }
  }

  @override
  void write(BinaryWriter writer, CallClassification obj) {
    switch (obj) {
      case CallClassification.personal:
        writer.writeByte(0);
        break;
      case CallClassification.business:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallClassificationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CallTypeAdapter extends TypeAdapter<CallType> {
  @override
  final int typeId = 9;

  @override
  CallType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CallType.incoming;
      case 1:
        return CallType.outgoing;
      case 2:
        return CallType.missed;
      case 3:
        return CallType.rejected;
      default:
        return CallType.incoming;
    }
  }

  @override
  void write(BinaryWriter writer, CallType obj) {
    switch (obj) {
      case CallType.incoming:
        writer.writeByte(0);
        break;
      case CallType.outgoing:
        writer.writeByte(1);
        break;
      case CallType.missed:
        writer.writeByte(2);
        break;
      case CallType.rejected:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
