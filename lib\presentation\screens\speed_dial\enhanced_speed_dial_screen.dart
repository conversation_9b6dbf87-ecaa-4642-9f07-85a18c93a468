import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_phone_direct_caller/flutter_phone_direct_caller.dart';
import 'package:vibration/vibration.dart';
import '../../providers/speed_dial_provider.dart';
import '../../providers/contact_provider.dart';
import '../../providers/enhanced_call_provider.dart';
import '../../widgets/speed_dial/speed_dial_grid.dart';
import '../../widgets/speed_dial/add_speed_dial_dialog.dart';
import '../../../data/models/contact_model.dart';

class EnhancedSpeedDialScreen extends StatefulWidget {
  const EnhancedSpeedDialScreen({super.key});

  @override
  State<EnhancedSpeedDialScreen> createState() =>
      _EnhancedSpeedDialScreenState();
}

class _EnhancedSpeedDialScreenState extends State<EnhancedSpeedDialScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isLoading = false;
  List<String> _searchFilters = [];

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _initializeData();
  }

  void _initializeData() async {
    setState(() => _isLoading = true);

    try {
      await context.read<SpeedDialProvider>().loadSpeedDialContacts();
      await context.read<ContactProvider>().loadContacts();

      _fadeController.forward();
      _slideController.forward();
    } catch (e) {
      _showErrorSnackBar('Failed to load speed dial contacts: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(context),
      body: _isLoading ? _buildLoadingState() : _buildMainContent(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        'Speed Dial',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.pop(context),
        tooltip: 'Back to Dialer',
      ),
      actions: [
        Consumer<SpeedDialProvider>(
          builder: (context, speedDialProvider, child) {
            return IconButton(
              icon: Icon(
                speedDialProvider.isEditMode ? Icons.done : Icons.edit,
                color: speedDialProvider.isEditMode
                    ? Theme.of(context).primaryColor
                    : null,
              ),
              onPressed: _toggleEditMode,
              tooltip: speedDialProvider.isEditMode
                  ? 'Done editing'
                  : 'Edit speed dial',
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () => _showSpeedDialOptions(context),
          tooltip: 'More options',
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading speed dial contacts...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          children: [
            // Search Bar
            _buildSearchBar(),

            // Search Filters (if search is active)
            if (_searchQuery.isNotEmpty) _buildSearchFilters(),

            // Header with instructions and stats
            _buildHeaderSection(),

            // Speed Dial Grid
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Consumer<SpeedDialProvider>(
                  builder: (context, speedDialProvider, child) {
                    return SpeedDialGrid(
                      speedDialContacts: speedDialProvider.speedDialContacts,
                      isEditMode: speedDialProvider.isEditMode,
                      onContactTap: _handleContactTap,
                      onEmptySlotTap: _addSpeedDialContact,
                      onRemoveContact: _removeSpeedDialContact,
                    );
                  },
                ),
              ),
            ),

            // Favorites Section (when not in edit mode and no search)
            if (_searchQuery.isEmpty) _buildFavoritesSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Consumer<SpeedDialProvider>(
      builder: (context, speedDialProvider, child) {
        if (speedDialProvider.isEditMode) {
          return FloatingActionButton.extended(
            onPressed: _autoAssignFavorites,
            icon: const Icon(Icons.auto_fix_high),
            label: const Text('Auto Fill'),
            tooltip: 'Auto-assign favorite contacts',
          );
        }

        return FloatingActionButton(
          onPressed: () => _showQuickActions(context),
          child: const Icon(Icons.add),
          tooltip: 'Quick actions',
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search speed dial contacts...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: _clearSearch,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
        ),
        onChanged: _onSearchChanged,
      ),
    );
  }

  Widget _buildSearchFilters() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: ['Favorites', 'Recent', 'Frequent', 'All'].map((filter) {
            final isActive = _searchFilters.contains(filter);
            return Container(
              margin: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(filter),
                selected: isActive,
                onSelected: (_) => _toggleFilter(filter),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Consumer<SpeedDialProvider>(
      builder: (context, speedDialProvider, child) {
        final stats = speedDialProvider.getSpeedDialStats();

        return Container(
          width: double.infinity,
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.speed,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Quick Access',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${stats['occupied_slots']}/${stats['total_slots']}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                speedDialProvider.isEditMode
                    ? 'Tap contacts to edit or remove speed dial contacts'
                    : 'Tap to call your favorite contacts instantly',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFavoritesSection() {
    return Consumer<SpeedDialProvider>(
      builder: (context, speedDialProvider, child) {
        if (speedDialProvider.isEditMode) return const SizedBox.shrink();

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.star,
                    color: Colors.amber.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Favorite Contacts',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: _importFavorites,
                    child: const Text('Import All'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Consumer<ContactProvider>(
                builder: (context, contactProvider, child) {
                  final favoriteContacts = contactProvider.favoriteContacts;

                  if (favoriteContacts.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.star_border,
                            size: 48,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'No favorite contacts yet',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Mark contacts as favorites to see them here',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  return SizedBox(
                    height: 80,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: favoriteContacts.length,
                      itemBuilder: (context, index) {
                        final contact = favoriteContacts[index];
                        return _buildFavoriteContactItem(contact);
                      },
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFavoriteContactItem(ContactModel contact) {
    return Container(
      width: 70,
      margin: const EdgeInsets.only(right: 12),
      child: Column(
        children: [
          GestureDetector(
            onTap: () => _callContact(contact),
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                gradient: LinearGradient(
                  colors: _getAvatarColors(contact.displayName),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: contact.avatar != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(25),
                      child: Image.memory(
                        contact.avatar!,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Center(
                      child: Text(
                        _getInitials(contact.displayName),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            contact.displayName.split(' ').first,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // Event Handlers
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
      _searchFilters.clear();
    });
  }

  void _toggleFilter(String filter) {
    setState(() {
      if (_searchFilters.contains(filter)) {
        _searchFilters.remove(filter);
      } else {
        _searchFilters.add(filter);
      }
    });
  }

  void _handleContactTap(ContactModel? contact, int position) {
    if (contact == null) return;

    final speedDialProvider = context.read<SpeedDialProvider>();
    if (speedDialProvider.isEditMode) {
      _showContactContextMenu(contact, position);
    } else {
      _callContact(contact);
    }
  }

  Future<void> _callContact(ContactModel contact) async {
    if (contact.phoneNumbers.isEmpty) {
      _showErrorSnackBar(
          'No phone number available for ${contact.displayName}');
      return;
    }

    try {
      // Add haptic feedback
      HapticFeedback.mediumImpact();

      // Add vibration if available
      final hasVibrator = await Vibration.hasVibrator();
      if (hasVibrator == true) {
        Vibration.vibrate(duration: 100);
      }

      final phoneNumber = contact.phoneNumbers.first.number;

      // Use enhanced call provider
      final callProvider = context.read<EnhancedCallProvider>();
      final success = await callProvider.makeCall(phoneNumber,
          contactName: contact.displayName);

      if (success) {
        // Update contact frequency
        await context
            .read<ContactProvider>()
            .updateContactFrequency(contact.id);
        _showSuccessSnackBar('Calling ${contact.displayName}...');
      } else {
        _showErrorSnackBar('Call was blocked or failed');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to make call: $e');
    }
  }

  void _showContactContextMenu(ContactModel contact, int position) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.call),
              title: const Text('Call'),
              onTap: () {
                Navigator.pop(context);
                _callContact(contact);
              },
            ),
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Contact'),
              onTap: () {
                Navigator.pop(context);
                _editContact(contact);
              },
            ),
            ListTile(
              leading: const Icon(Icons.remove_circle, color: Colors.red),
              title: const Text('Remove'),
              onTap: () {
                Navigator.pop(context);
                _removeSpeedDialContact(position);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _editContact(ContactModel contact) {
    _showInfoSnackBar('Contact editing coming soon!');
  }

  // Speed Dial Management Methods
  void _addSpeedDialContact(int position) {
    showDialog(
      context: context,
      builder: (context) => AddSpeedDialDialog(position: position),
    );
  }

  Future<void> _removeSpeedDialContact(int position) async {
    try {
      final speedDialProvider = context.read<SpeedDialProvider>();
      final contact = speedDialProvider.getSpeedDialContact(position);

      if (contact == null) return;

      final confirmed = await _showConfirmDialog(
        'Remove Speed Dial',
        'Remove ${contact.displayName} from speed dial position $position?',
        confirmText: 'Remove',
        isDestructive: true,
      );

      if (confirmed) {
        await speedDialProvider.removeSpeedDialContact(position);
        _showSuccessSnackBar('${contact.displayName} removed from speed dial');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to remove contact: $e');
    }
  }

  void _toggleEditMode() {
    HapticFeedback.lightImpact();
    context.read<SpeedDialProvider>().toggleEditMode();
  }

  void _showSpeedDialOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.star),
              title: const Text('Import Favorites'),
              onTap: () {
                Navigator.pop(context);
                _importFavorites();
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear_all),
              title: const Text('Clear All'),
              onTap: () {
                Navigator.pop(context);
                _clearAllSpeedDial();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _importFavorites() async {
    try {
      await context.read<SpeedDialProvider>().autoAssignFavoriteContacts();
      _showSuccessSnackBar('Favorite contacts imported to speed dial');
    } catch (e) {
      _showErrorSnackBar('Failed to import favorites: $e');
    }
  }

  Future<void> _clearAllSpeedDial() async {
    try {
      final confirmed = await _showConfirmDialog(
        'Clear All Speed Dial',
        'Are you sure you want to remove all speed dial contacts? This action cannot be undone.',
        confirmText: 'Clear All',
        isDestructive: true,
      );

      if (confirmed) {
        await context.read<SpeedDialProvider>().clearAllSpeedDial();
        _showSuccessSnackBar('All speed dial contacts cleared');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to clear speed dial: $e');
    }
  }

  Future<void> _autoAssignFavorites() async {
    try {
      await context.read<SpeedDialProvider>().autoAssignFavoriteContacts();
      _showSuccessSnackBar('Speed dial auto-filled with favorite contacts');
    } catch (e) {
      _showErrorSnackBar('Failed to auto-fill speed dial: $e');
    }
  }

  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.star, color: Colors.amber),
              title: const Text('Import Favorites'),
              subtitle: const Text('Add favorite contacts to speed dial'),
              onTap: () {
                Navigator.pop(context);
                _importFavorites();
              },
            ),
            ListTile(
              leading: const Icon(Icons.auto_fix_high, color: Colors.blue),
              title: const Text('Auto Fill'),
              subtitle: const Text('Automatically assign most used contacts'),
              onTap: () {
                Navigator.pop(context);
                _autoAssignFavorites();
              },
            ),
          ],
        ),
      ),
    );
  }

  // Utility Methods
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Future<bool> _showConfirmDialog(
    String title,
    String content, {
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    bool isDestructive = false,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: isDestructive ? Colors.red : null,
              foregroundColor: isDestructive ? Colors.white : null,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';

    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  List<Color> _getAvatarColors(String name) {
    final hash = name.hashCode;
    final colors = [
      [Colors.blue.shade400, Colors.blue.shade600],
      [Colors.green.shade400, Colors.green.shade600],
      [Colors.purple.shade400, Colors.purple.shade600],
      [Colors.orange.shade400, Colors.orange.shade600],
      [Colors.teal.shade400, Colors.teal.shade600],
      [Colors.pink.shade400, Colors.pink.shade600],
      [Colors.indigo.shade400, Colors.indigo.shade600],
      [Colors.red.shade400, Colors.red.shade600],
    ];

    return colors[hash.abs() % colors.length];
  }
}
