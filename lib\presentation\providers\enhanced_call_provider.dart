import 'package:flutter/material.dart';
import 'package:flutter_phone_direct_caller/flutter_phone_direct_caller.dart';
import '../../data/services/call_recording_service.dart';
import '../../data/services/call_blocking_service.dart';
import '../../data/models/call_log_model.dart';
import '../../data/models/blocked_number_model.dart';
import '../../data/services/local_storage_service.dart';

enum CallState {
  idle,
  dialing,
  ringing,
  connected,
  onHold,
  ended,
  blocked,
}

class EnhancedCallProvider with ChangeNotifier {
  final CallRecordingService _recordingService = CallRecordingService();
  final CallBlockingService _blockingService = CallBlockingService();
  final LocalStorageService _localStorage = LocalStorageService();

  CallState _currentCallState = CallState.idle;
  String? _currentCallId;
  String? _currentPhoneNumber;
  String? _currentContactName;
  DateTime? _callStartTime;
  bool _isRecording = false;
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  List<BlockedNumber> _blockedNumbers = [];
  List<CallRecording> _recordings = [];

  // Getters
  CallState get currentCallState => _currentCallState;
  String? get currentCallId => _currentCallId;
  String? get currentPhoneNumber => _currentPhoneNumber;
  String? get currentContactName => _currentContactName;
  DateTime? get callStartTime => _callStartTime;
  bool get isRecording => _isRecording;
  bool get isMuted => _isMuted;
  bool get isSpeakerOn => _isSpeakerOn;
  List<BlockedNumber> get blockedNumbers => _blockedNumbers;
  List<CallRecording> get recordings => _recordings;

  Duration? get callDuration {
    if (_callStartTime == null) return null;
    return DateTime.now().difference(_callStartTime!);
  }

  EnhancedCallProvider() {
    _loadBlockedNumbers();
    _loadRecordings();
  }

  Future<void> _loadBlockedNumbers() async {
    try {
      _blockedNumbers = await _blockingService.getBlockedNumbers();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading blocked numbers: $e');
    }
  }

  Future<void> _loadRecordings() async {
    try {
      _recordings = await _recordingService.getRecordings();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading recordings: $e');
    }
  }

  Future<bool> makeCall(String phoneNumber, {String? contactName}) async {
    try {
      // Check if number should be blocked
      if (await _blockingService.shouldBlockCall(phoneNumber, contactName: contactName)) {
        _currentCallState = CallState.blocked;
        notifyListeners();
        return false;
      }

      _currentCallId = DateTime.now().millisecondsSinceEpoch.toString();
      _currentPhoneNumber = phoneNumber;
      _currentContactName = contactName;
      _currentCallState = CallState.dialing;
      notifyListeners();

      // Make the call
      await FlutterPhoneDirectCaller.callNumber(phoneNumber);

      // Simulate call states (in real app, this would be handled by call events)
      await Future.delayed(const Duration(seconds: 1));
      _currentCallState = CallState.ringing;
      notifyListeners();

      // Start recording if enabled
      if (await _recordingService.isRecordingEnabled()) {
        await startRecording();
      }

      return true;
    } catch (e) {
      debugPrint('Error making call: $e');
      _currentCallState = CallState.idle;
      notifyListeners();
      return false;
    }
  }

  Future<void> answerCall() async {
    try {
      _currentCallState = CallState.connected;
      _callStartTime = DateTime.now();
      notifyListeners();

      // Start recording if enabled
      if (await _recordingService.isRecordingEnabled()) {
        await startRecording();
      }
    } catch (e) {
      debugPrint('Error answering call: $e');
    }
  }

  Future<void> endCall() async {
    try {
      // Stop recording if active
      if (_isRecording) {
        await stopRecording();
      }

      // Log the call
      if (_currentCallId != null && _currentPhoneNumber != null) {
        final duration = callDuration?.inSeconds ?? 0;
        final callLog = CallLogModel(
          id: _currentCallId!,
          number: _currentPhoneNumber!,
          name: _currentContactName,
          type: CallType.outgoing,
          timestamp: _callStartTime ?? DateTime.now(),
          duration: duration,
          isAnswered: _currentCallState == CallState.connected,
        );

        await _localStorage.saveCallLog(callLog);
      }

      _currentCallState = CallState.ended;
      notifyListeners();

      // Reset call state after a delay
      await Future.delayed(const Duration(seconds: 2));
      _resetCallState();
    } catch (e) {
      debugPrint('Error ending call: $e');
    }
  }

  Future<void> holdCall() async {
    try {
      if (_currentCallState == CallState.connected) {
        _currentCallState = CallState.onHold;
        
        // Pause recording if active
        if (_isRecording) {
          await _recordingService.pauseRecording();
        }
        
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error holding call: $e');
    }
  }

  Future<void> resumeCall() async {
    try {
      if (_currentCallState == CallState.onHold) {
        _currentCallState = CallState.connected;
        
        // Resume recording if active
        if (_isRecording) {
          await _recordingService.resumeRecording();
        }
        
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error resuming call: $e');
    }
  }

  Future<void> toggleMute() async {
    try {
      _isMuted = !_isMuted;
      notifyListeners();
      
      // TODO: Implement actual mute functionality
      debugPrint('Call ${_isMuted ? 'muted' : 'unmuted'}');
    } catch (e) {
      debugPrint('Error toggling mute: $e');
    }
  }

  Future<void> toggleSpeaker() async {
    try {
      _isSpeakerOn = !_isSpeakerOn;
      notifyListeners();
      
      // TODO: Implement actual speaker functionality
      debugPrint('Speaker ${_isSpeakerOn ? 'on' : 'off'}');
    } catch (e) {
      debugPrint('Error toggling speaker: $e');
    }
  }

  Future<void> startRecording() async {
    try {
      if (_currentCallId != null && _currentPhoneNumber != null) {
        final success = await _recordingService.startRecording(
          _currentCallId!,
          _currentPhoneNumber!,
        );
        
        if (success) {
          _isRecording = true;
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error starting recording: $e');
    }
  }

  Future<void> stopRecording() async {
    try {
      await _recordingService.stopRecording();
      _isRecording = false;
      notifyListeners();
      
      // Reload recordings list
      await _loadRecordings();
    } catch (e) {
      debugPrint('Error stopping recording: $e');
    }
  }

  Future<void> blockNumber(String phoneNumber, {String? reason}) async {
    try {
      await _blockingService.blockNumber(phoneNumber, reason: reason);
      await _loadBlockedNumbers();
    } catch (e) {
      debugPrint('Error blocking number: $e');
      rethrow;
    }
  }

  Future<void> unblockNumber(String phoneNumber) async {
    try {
      await _blockingService.unblockNumber(phoneNumber);
      await _loadBlockedNumbers();
    } catch (e) {
      debugPrint('Error unblocking number: $e');
      rethrow;
    }
  }

  Future<bool> isNumberBlocked(String phoneNumber) async {
    return await _blockingService.isNumberBlocked(phoneNumber);
  }

  Future<void> reportSpam(String phoneNumber, String reason) async {
    try {
      await _blockingService.reportSpam(phoneNumber, reason);
      await _loadBlockedNumbers();
    } catch (e) {
      debugPrint('Error reporting spam: $e');
      rethrow;
    }
  }

  Future<void> deleteRecording(String path) async {
    try {
      await _recordingService.deleteRecording(path);
      await _loadRecordings();
    } catch (e) {
      debugPrint('Error deleting recording: $e');
      rethrow;
    }
  }

  void _resetCallState() {
    _currentCallState = CallState.idle;
    _currentCallId = null;
    _currentPhoneNumber = null;
    _currentContactName = null;
    _callStartTime = null;
    _isRecording = false;
    _isMuted = false;
    _isSpeakerOn = false;
    notifyListeners();
  }

  // Conference call functionality
  final List<String> _conferenceParticipants = [];
  List<String> get conferenceParticipants => _conferenceParticipants;

  Future<void> addToConference(String phoneNumber) async {
    try {
      if (!_conferenceParticipants.contains(phoneNumber)) {
        _conferenceParticipants.add(phoneNumber);
        notifyListeners();
        
        // TODO: Implement actual conference call functionality
        debugPrint('Added $phoneNumber to conference');
      }
    } catch (e) {
      debugPrint('Error adding to conference: $e');
    }
  }

  Future<void> removeFromConference(String phoneNumber) async {
    try {
      _conferenceParticipants.remove(phoneNumber);
      notifyListeners();
      
      // TODO: Implement actual conference call functionality
      debugPrint('Removed $phoneNumber from conference');
    } catch (e) {
      debugPrint('Error removing from conference: $e');
    }
  }

  Future<void> startConferenceCall(List<String> phoneNumbers) async {
    try {
      _conferenceParticipants.clear();
      _conferenceParticipants.addAll(phoneNumbers);
      
      // TODO: Implement actual conference call functionality
      debugPrint('Started conference call with ${phoneNumbers.length} participants');
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error starting conference call: $e');
    }
  }

  @override
  void dispose() {
    _recordingService.dispose();
    super.dispose();
  }
}
