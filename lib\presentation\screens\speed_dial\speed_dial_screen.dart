import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:vibration/vibration.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../providers/speed_dial_provider.dart';
import '../../providers/contact_provider.dart';
import '../../providers/enhanced_call_provider.dart';
import '../../../data/models/contact_model.dart';

class SpeedDialScreen extends StatefulWidget {
  const SpeedDialScreen({super.key});

  @override
  State<SpeedDialScreen> createState() => _SpeedDialScreenState();
}

class _SpeedDialScreenState extends State<SpeedDialScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  final TextEditingController _searchController = TextEditingController();

  String _searchQuery = '';
  bool _isLoading = false;
  List<String> _searchFilters = [];
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeData();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _initializeData() async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    try {
      final speedDialProvider = context.read<SpeedDialProvider>();
      final contactProvider = context.read<ContactProvider>();

      await Future.wait([
        speedDialProvider.loadSpeedDialContacts(),
        contactProvider.loadContacts(),
      ]);

      if (mounted) {
        _fadeController.forward();
        _slideController.forward();
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to load speed dial contacts: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingState() : _buildMainContent(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Speed Dial',
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
      ),
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_rounded),
        onPressed: () => Navigator.pop(context),
        tooltip: 'Back',
      ),
      actions: [
        // View Toggle
        IconButton(
          icon: Icon(
            _isGridView ? Icons.view_list_rounded : Icons.grid_view_rounded,
            color: Colors.grey.shade700,
          ),
          onPressed: () {
            if (mounted) {
              setState(() => _isGridView = !_isGridView);
              HapticFeedback.lightImpact();
            }
          },
          tooltip: _isGridView ? 'List View' : 'Grid View',
        ),
        // Edit Mode Toggle
        Consumer<SpeedDialProvider>(
          builder: (context, speedDialProvider, child) {
            return IconButton(
              icon: Icon(
                speedDialProvider.isEditMode
                    ? Icons.done_rounded
                    : Icons.edit_rounded,
                color: speedDialProvider.isEditMode
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade700,
              ),
              onPressed: _toggleEditMode,
              tooltip: speedDialProvider.isEditMode
                  ? 'Done editing'
                  : 'Edit speed dial',
            );
          },
        ),
        // More Options
        PopupMenuButton<String>(
          icon: Icon(Icons.more_vert_rounded, color: Colors.grey.shade700),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'import_favorites',
              child: Row(
                children: [
                  Icon(Icons.star_rounded, color: Colors.amber),
                  SizedBox(width: 8),
                  Text('Import Favorites'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'auto_fill',
              child: Row(
                children: [
                  Icon(Icons.auto_fix_high_rounded, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('Auto Fill'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'clear_all',
              child: Row(
                children: [
                  Icon(Icons.clear_all_rounded, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Clear All'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings_rounded, color: Colors.grey),
                  SizedBox(width: 8),
                  Text('Settings'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading speed dial contacts...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          children: [
            // Search Bar
            _buildSearchBar(),

            // Search Filters
            if (_searchQuery.isNotEmpty) _buildSearchFilters(),

            // Header with stats
            _buildHeaderSection(),

            // Speed Dial Content
            Expanded(
              child: _isGridView ? _buildGridView() : _buildListView(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Search speed dial contacts...',
          prefixIcon: Icon(Icons.search_rounded, color: Colors.grey.shade500),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear_rounded, color: Colors.grey.shade500),
                  onPressed: _clearSearch,
                )
              : null,
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
      ),
    );
  }

  Widget _buildSearchFilters() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: 50,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('All', 'all'),
          _buildFilterChip('Favorites', 'favorites'),
          _buildFilterChip('Recent', 'recent'),
          _buildFilterChip('Frequent', 'frequent'),
          if (_searchFilters.isNotEmpty) ...[
            const SizedBox(width: 8),
            TextButton.icon(
              onPressed: _clearAllFilters,
              icon: const Icon(Icons.clear_rounded, size: 16),
              label: const Text('Clear'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
                backgroundColor: Colors.red.shade50,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _searchFilters.contains(value);
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (_) => _toggleFilter(value),
        selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
        checkmarkColor: Theme.of(context).primaryColor,
        backgroundColor: Colors.white,
        side: BorderSide(
          color: isSelected
              ? Theme.of(context).primaryColor
              : Colors.grey.shade300,
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Consumer<SpeedDialProvider>(
      builder: (context, speedDialProvider, child) {
        final stats = speedDialProvider.getSpeedDialStats();

        return Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor.withOpacity(0.1),
                Theme.of(context).primaryColor.withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Theme.of(context).primaryColor.withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.speed_rounded,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Quick Access',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${stats['occupied_slots'] ?? 0}/${stats['total_slots'] ?? 9}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                speedDialProvider.isEditMode
                    ? 'Tap to edit, long press for options, or drag to reorder'
                    : 'Tap to call your favorite contacts instantly',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
              if (speedDialProvider.isEditMode) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline_rounded,
                      size: 16,
                      color: Colors.amber.shade600,
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        'Use Auto Fill to quickly assign favorite contacts',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.amber.shade700,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildGridView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Consumer<SpeedDialProvider>(
        builder: (context, speedDialProvider, child) {
          return GridView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 0.85,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: 9,
            itemBuilder: (context, index) {
              final position = index + 1;
              final contact = speedDialProvider.getSpeedDialContact(position);
              return _buildSpeedDialCard(
                  contact, position, speedDialProvider.isEditMode);
            },
          );
        },
      ),
    );
  }

  Widget _buildListView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Consumer<SpeedDialProvider>(
        builder: (context, speedDialProvider, child) {
          return ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: 9,
            itemBuilder: (context, index) {
              final position = index + 1;
              final contact = speedDialProvider.getSpeedDialContact(position);
              return _buildSpeedDialListItem(
                  contact, position, speedDialProvider.isEditMode);
            },
          );
        },
      ),
    );
  }

  Widget _buildSpeedDialCard(
      ContactModel? contact, int position, bool isEditMode) {
    return GestureDetector(
      onTap: () => _handleContactTap(contact, position),
      onLongPress: contact != null
          ? () => _showContactContextMenu(contact, position)
          : null,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Avatar Section
            Stack(
              children: [
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(28),
                    gradient: contact != null
                        ? LinearGradient(
                            colors: _getAvatarColors(contact.displayName),
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : LinearGradient(
                            colors: [
                              Colors.grey.shade200,
                              Colors.grey.shade300
                            ],
                          ),
                  ),
                  child: contact != null
                      ? _buildContactAvatar(contact, 56)
                      : Icon(
                          Icons.add_rounded,
                          color: Colors.grey.shade500,
                          size: 28,
                        ),
                ),
                // Edit mode overlay
                if (isEditMode && contact != null)
                  Positioned(
                    right: -2,
                    top: -2,
                    child: GestureDetector(
                      onTap: () => _removeSpeedDialContact(position),
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.remove_rounded,
                          color: Colors.white,
                          size: 14,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),

            // Position Badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                position.toString(),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            const SizedBox(height: 4),

            // Contact Name
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Text(
                contact?.displayName.split(' ').first ?? 'Add Contact',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color:
                      contact != null ? Colors.black87 : Colors.grey.shade500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpeedDialListItem(
      ContactModel? contact, int position, bool isEditMode) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Stack(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                gradient: contact != null
                    ? LinearGradient(
                        colors: _getAvatarColors(contact.displayName),
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : LinearGradient(
                        colors: [Colors.grey.shade200, Colors.grey.shade300],
                      ),
              ),
              child: contact != null
                  ? _buildContactAvatar(contact, 48)
                  : Icon(
                      Icons.add_rounded,
                      color: Colors.grey.shade500,
                      size: 24,
                    ),
            ),
            // Position Badge
            Positioned(
              right: -2,
              bottom: -2,
              child: Container(
                width: 18,
                height: 18,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: Center(
                  child: Text(
                    position.toString(),
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        title: Text(
          contact?.displayName ?? 'Add Contact',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: contact != null ? Colors.black87 : Colors.grey.shade500,
          ),
        ),
        subtitle: contact != null && contact.phoneNumbers?.isNotEmpty == true
            ? Text(
                contact.phoneNumbers!.first.number,
                style: TextStyle(color: Colors.grey.shade600),
              )
            : Text(
                'Tap to add contact',
                style: TextStyle(color: Colors.grey.shade400),
              ),
        trailing: isEditMode && contact != null
            ? IconButton(
                onPressed: () => _removeSpeedDialContact(position),
                icon:
                    const Icon(Icons.remove_circle_rounded, color: Colors.red),
              )
            : contact != null
                ? Icon(Icons.call_rounded,
                    color: Theme.of(context).primaryColor)
                : Icon(Icons.add_circle_outline_rounded,
                    color: Colors.grey.shade400),
        onTap: () => _handleContactTap(contact, position),
        onLongPress: contact != null
            ? () => _showContactContextMenu(contact, position)
            : null,
      ),
    );
  }

  Widget _buildContactAvatar(ContactModel contact, double size) {
    if (contact.avatar != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(size / 2),
        child: Image.memory(
          contact.avatar!,
          width: size,
          height: size,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Center(
              child: Text(
                _getInitials(contact.displayName),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: size * 0.35,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          },
        ),
      );
    } else {
      return Center(
        child: Text(
          _getInitials(contact.displayName),
          style: TextStyle(
            color: Colors.white,
            fontSize: size * 0.35,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
  }

  Widget _buildFloatingActionButton() {
    return Consumer<SpeedDialProvider>(
      builder: (context, speedDialProvider, child) {
        return FloatingActionButton(
          onPressed: () {
            speedDialProvider.toggleEditMode();
          },
          tooltip: speedDialProvider.isEditMode
              ? 'Exit edit mode'
              : 'Edit speed dial',
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          child: Icon(
            speedDialProvider.isEditMode ? Icons.check : Icons.edit,
          ),
        );
      },
    );
  }

  // Event Handlers
  void _onSearchChanged(String query) {
    if (mounted) {
      setState(() {
        _searchQuery = query;
      });
    }
  }

  void _clearSearch() {
    if (mounted) {
      setState(() {
        _searchQuery = '';
        _searchFilters.clear();
      });
      _searchController.clear();
    }
  }

  void _toggleFilter(String filter) {
    if (mounted) {
      setState(() {
        if (_searchFilters.contains(filter)) {
          _searchFilters.remove(filter);
        } else {
          _searchFilters.add(filter);
        }
      });
      HapticFeedback.selectionClick();
    }
  }

  void _clearAllFilters() {
    if (mounted) {
      setState(() {
        _searchFilters.clear();
      });
      HapticFeedback.lightImpact();
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'clear_all':
        _clearAllSpeedDial();
        break;
      case 'settings':
        _openSettings();
        break;
    }
  }

  void _handleContactTap(ContactModel? contact, int position) {
    try {
      final speedDialProvider = context.read<SpeedDialProvider>();

      if (contact == null) {
        _addSpeedDialContact(position);
      } else if (speedDialProvider.isEditMode) {
        _showContactContextMenu(contact, position);
      } else {
        _callContact(contact);
      }
    } catch (e) {
      _showErrorSnackBar('Error handling contact tap: $e');
    }
  }

  Future<void> _callContact(ContactModel contact) async {
    if (contact.phoneNumbers?.isEmpty ?? true) {
      _showErrorSnackBar(
          'No phone number available for ${contact.displayName}');
      return;
    }

    try {
      await HapticFeedback.mediumImpact();

      final hasVibrator = await Vibration.hasVibrator();
      if (hasVibrator == true) {
        await Vibration.vibrate(duration: 100);
      }

      final phoneNumber = contact.phoneNumbers!.first.number;
      final Uri callUri = Uri(scheme: 'tel', path: phoneNumber);

      if (await canLaunchUrl(callUri)) {
        await launchUrl(callUri);

        if (mounted) {
          try {
            await context
                .read<ContactProvider>()
                .updateContactFrequency(contact.id);
          } catch (e) {
            // Silently handle frequency update failure
            debugPrint('Failed to update contact frequency: $e');
          }
          _showSuccessSnackBar('Calling ${contact.displayName}...');
        }
      } else {
        _showErrorSnackBar('Could not launch phone call');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to make call: $e');
    }
  }

  void _showContactContextMenu(ContactModel contact, int position) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Contact Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30),
                      gradient: LinearGradient(
                        colors: _getAvatarColors(contact.displayName),
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: _buildContactAvatar(contact, 60),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          contact.displayName,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (contact.phoneNumbers?.isNotEmpty == true)
                          Text(
                            contact.phoneNumbers!.first.number,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Position $position',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Action Buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  _buildActionButton(
                    icon: Icons.call_rounded,
                    label: 'Call',
                    color: Colors.green,
                    onTap: () {
                      Navigator.pop(context);
                      _callContact(contact);
                    },
                  ),
                  _buildActionButton(
                    icon: Icons.message_rounded,
                    label: 'Message',
                    color: Colors.blue,
                    onTap: () {
                      Navigator.pop(context);
                      _messageContact(contact);
                    },
                  ),
                  _buildActionButton(
                    icon: Icons.star_rounded,
                    label: contact.isFavorite
                        ? 'Remove from Favorites'
                        : 'Add to Favorites',
                    color: Colors.amber,
                    onTap: () {
                      Navigator.pop(context);
                      _toggleFavorite(contact);
                    },
                  ),
                  _buildActionButton(
                    icon: Icons.swap_horiz_rounded,
                    label: 'Change Position',
                    color: Colors.orange,
                    onTap: () {
                      Navigator.pop(context);
                      _showMovePositionDialog(contact, position);
                    },
                  ),
                  _buildActionButton(
                    icon: Icons.edit_rounded,
                    label: 'Edit Contact',
                    color: Colors.purple,
                    onTap: () {
                      Navigator.pop(context);
                      _editContact(contact);
                    },
                  ),
                  _buildActionButton(
                    icon: Icons.remove_circle_rounded,
                    label: 'Remove from Speed Dial',
                    color: Colors.red,
                    onTap: () {
                      Navigator.pop(context);
                      _removeSpeedDialContact(position);
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            decoration: BoxDecoration(
              color: color.withOpacity(0.08),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withOpacity(0.2)),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 22),
                const SizedBox(width: 12),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _messageContact(ContactModel contact) async {
    if (contact.phoneNumbers?.isEmpty ?? true) {
      _showErrorSnackBar(
          'No phone number available for ${contact.displayName}');
      return;
    }

    try {
      final phoneNumber = contact.phoneNumbers!.first.number;
      final Uri smsUri = Uri(scheme: 'sms', path: phoneNumber);

      if (await canLaunchUrl(smsUri)) {
        await launchUrl(smsUri);
      } else {
        _showErrorSnackBar('Could not launch SMS');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to send message: $e');
    }
  }

  void _editContact(ContactModel contact) {
    _showInfoSnackBar('Contact editing feature coming soon!');
  }

  Future<void> _toggleFavorite(ContactModel contact) async {
    try {
      await context.read<ContactProvider>().toggleFavorite(contact.id);
      final isFavorite = !contact.isFavorite;
      _showSuccessSnackBar(
        isFavorite
            ? '${contact.displayName} added to favorites'
            : '${contact.displayName} removed from favorites',
      );
    } catch (e) {
      _showErrorSnackBar('Failed to update favorite status: $e');
    }
  }

  void _showMovePositionDialog(ContactModel contact, int currentPosition) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text('Move ${contact.displayName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Select a new position for ${contact.displayName}:'),
            const SizedBox(height: 20),
            Consumer<SpeedDialProvider>(
              builder: (context, speedDialProvider, child) {
                return GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 1,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: 9,
                  itemBuilder: (context, index) {
                    final position = index + 1;
                    final isOccupied =
                        speedDialProvider.getSpeedDialContact(position) != null;
                    final isCurrent = position == currentPosition;

                    return GestureDetector(
                      onTap: isCurrent
                          ? null
                          : () {
                              Navigator.pop(context);
                              _moveSpeedDialContact(
                                  contact, currentPosition, position);
                            },
                      child: Container(
                        decoration: BoxDecoration(
                          color: isCurrent
                              ? Colors.blue.shade100
                              : isOccupied
                                  ? Colors.red.shade100
                                  : Colors.green.shade100,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isCurrent
                                ? Colors.blue
                                : isOccupied
                                    ? Colors.red
                                    : Colors.green,
                            width: 2,
                          ),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                position.toString(),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: isCurrent
                                      ? Colors.blue
                                      : isOccupied
                                          ? Colors.red
                                          : Colors.green,
                                ),
                              ),
                              Text(
                                isCurrent
                                    ? 'Current'
                                    : isOccupied
                                        ? 'Occupied'
                                        : 'Available',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: isCurrent
                                      ? Colors.blue
                                      : isOccupied
                                          ? Colors.red
                                          : Colors.green,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _moveSpeedDialContact(ContactModel contact, int from, int to) async {
    try {
      final speedDialProvider = context.read<SpeedDialProvider>();
      final existingContact = speedDialProvider.getSpeedDialContact(to);

      if (existingContact != null) {
        final confirmed = await _showConfirmDialog(
          'Replace Contact',
          'Position $to is occupied by ${existingContact.displayName}. '
              'Do you want to replace it with ${contact.displayName}?',
          confirmText: 'Replace',
        );

        if (!confirmed) return;
      }

      await speedDialProvider.removeSpeedDialContact(from);
      await speedDialProvider.setSpeedDialContact(contact, to);

      _showSuccessSnackBar('${contact.displayName} moved to position $to');
    } catch (e) {
      _showErrorSnackBar('Failed to move contact: $e');
    }
  }

  // Speed Dial Management Methods
  void _addSpeedDialContact(int position) {
    showDialog(
      context: context,
      builder: (context) => AddSpeedDialDialog(position: position),
    );
  }

  Future<void> _removeSpeedDialContact(int position) async {
    try {
      final speedDialProvider = context.read<SpeedDialProvider>();
      final contact = speedDialProvider.getSpeedDialContact(position);

      if (contact == null) return;

      final confirmed = await _showConfirmDialog(
        'Remove Speed Dial',
        'Remove ${contact.displayName} from speed dial position $position?',
        confirmText: 'Remove',
        isDestructive: true,
      );

      if (confirmed) {
        await speedDialProvider.removeSpeedDialContact(position);
        _showSuccessSnackBar('${contact.displayName} removed from speed dial');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to remove contact: $e');
    }
  }

  void _toggleEditMode() {
    try {
      HapticFeedback.lightImpact();
      context.read<SpeedDialProvider>().toggleEditMode();
    } catch (e) {
      _showErrorSnackBar('Failed to toggle edit mode: $e');
    }
  }

  Future<void> _clearAllSpeedDial() async {
    try {
      final confirmed = await _showConfirmDialog(
        'Clear All Speed Dial',
        'Are you sure you want to remove all speed dial contacts? This action cannot be undone.',
        confirmText: 'Clear All',
        isDestructive: true,
      );

      if (confirmed) {
        await context.read<SpeedDialProvider>().clearAllSpeedDial();
        _showSuccessSnackBar('All speed dial contacts cleared');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to clear speed dial: $e');
    }
  }

  void _openSettings() {
    _showInfoSnackBar('Speed dial settings coming soon!');
  }

  // Utility Methods
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_rounded, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_rounded, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_rounded, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<bool> _showConfirmDialog(
    String title,
    String content, {
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    bool isDestructive = false,
  }) async {
    if (!mounted) return false;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isDestructive ? Colors.red : Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  List<Color> _getAvatarColors(String name) {
    final hash = name.hashCode;
    final colors = [
      [Colors.blue.shade400, Colors.blue.shade600],
      [Colors.green.shade400, Colors.green.shade600],
      [Colors.purple.shade400, Colors.purple.shade600],
      [Colors.orange.shade400, Colors.orange.shade600],
      [Colors.teal.shade400, Colors.teal.shade600],
      [Colors.pink.shade400, Colors.pink.shade600],
      [Colors.indigo.shade400, Colors.indigo.shade600],
      [Colors.red.shade400, Colors.red.shade600],
    ];
    return colors[hash.abs() % colors.length];
  }
}

// Add Speed Dial Dialog
class AddSpeedDialDialog extends StatefulWidget {
  final int position;

  const AddSpeedDialDialog({super.key, required this.position});

  @override
  State<AddSpeedDialDialog> createState() => _AddSpeedDialDialogState();
}

class _AddSpeedDialDialogState extends State<AddSpeedDialDialog> {
  String _searchQuery = '';
  List<ContactModel> _filteredContacts = [];
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadContacts();
  }

  void _loadContacts() async {
    try {
      final contactProvider = context.read<ContactProvider>();
      final allContacts = contactProvider.contacts ?? [];

      if (mounted) {
        setState(() {
          _filteredContacts = allContacts
              .where((contact) => contact.phoneNumbers?.isNotEmpty == true)
              .toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterContacts(String query) {
    final contactProvider = context.read<ContactProvider>();
    final allContacts = contactProvider.contacts ?? [];

    if (mounted) {
      setState(() {
        _searchQuery = query;
        if (query.isEmpty) {
          _filteredContacts = allContacts
              .where((contact) => contact.phoneNumbers?.isNotEmpty == true)
              .toList();
        } else {
          _filteredContacts = allContacts
              .where((contact) =>
                  (contact.phoneNumbers?.isNotEmpty == true) &&
                  (contact.displayName
                          .toLowerCase()
                          .contains(query.toLowerCase()) ||
                      contact.phoneNumbers!
                          .any((phone) => phone.number.contains(query))))
              .toList();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.person_add_rounded,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Add to Position ${widget.position}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close_rounded),
                  ),
                ],
              ),
            ),

            // Search Bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                onChanged: _filterContacts,
                decoration: InputDecoration(
                  hintText: 'Search contacts...',
                  prefixIcon: const Icon(Icons.search_rounded),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear_rounded),
                          onPressed: () {
                            _searchController.clear();
                            _filterContacts('');
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey.shade100,
                ),
              ),
            ),

            // Contact List
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredContacts.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.search_off_rounded,
                                size: 64,
                                color: Colors.grey.shade400,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No contacts found',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: _filteredContacts.length,
                          itemBuilder: (context, index) {
                            final contact = _filteredContacts[index];
                            return Container(
                              margin: const EdgeInsets.only(bottom: 8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.grey.shade200),
                              ),
                              child: ListTile(
                                leading: Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(24),
                                    gradient: LinearGradient(
                                      colors:
                                          _getAvatarColors(contact.displayName),
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                  ),
                                  child: contact.avatar != null
                                      ? ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(24),
                                          child: Image.memory(
                                            contact.avatar!,
                                            fit: BoxFit.cover,
                                            errorBuilder:
                                                (context, error, stackTrace) {
                                              return Center(
                                                child: Text(
                                                  _getInitials(
                                                      contact.displayName),
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        )
                                      : Center(
                                          child: Text(
                                            _getInitials(contact.displayName),
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                ),
                                title: Text(
                                  contact.displayName,
                                  style: const TextStyle(
                                      fontWeight: FontWeight.w600),
                                ),
                                subtitle: Text(
                                  contact.phoneNumbers?.first.number ?? '',
                                  style: TextStyle(color: Colors.grey.shade600),
                                ),
                                trailing: Icon(
                                  Icons.add_circle_rounded,
                                  color: Theme.of(context).primaryColor,
                                ),
                                onTap: () => _addContact(contact),
                              ),
                            );
                          },
                        ),
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _addContact(ContactModel contact) async {
    try {
      await context
          .read<SpeedDialProvider>()
          .setSpeedDialContact(contact, widget.position);

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle_rounded, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                      '${contact.displayName} added to position ${widget.position}'),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_rounded, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(child: Text('Failed to add contact: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    }
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  List<Color> _getAvatarColors(String name) {
    final hash = name.hashCode;
    final colors = [
      [Colors.blue.shade400, Colors.blue.shade600],
      [Colors.green.shade400, Colors.green.shade600],
      [Colors.purple.shade400, Colors.purple.shade600],
      [Colors.orange.shade400, Colors.orange.shade600],
      [Colors.teal.shade400, Colors.teal.shade600],
      [Colors.pink.shade400, Colors.pink.shade600],
      [Colors.indigo.shade400, Colors.indigo.shade600],
      [Colors.red.shade400, Colors.red.shade600],
    ];
    return colors[hash.abs() % colors.length];
  }
}
