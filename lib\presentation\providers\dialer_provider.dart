import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../data/models/contact_model.dart';
import '../../data/models/call_log_model.dart';
import '../../data/services/contact_service.dart';
import '../../data/services/local_storage_service.dart';

class DialerProvider with ChangeNotifier {
  final ContactService _contactService = ContactService();
  final LocalStorageService _localStorage = LocalStorageService();

  String _phoneNumber = '';
  List<ContactModel> _contacts = [];
  List<ContactModel> _filteredContacts = [];
  ContactModel? _matchedContact;
  bool _isLoading = false;

  String get phoneNumber => _phoneNumber;
  String get formattedPhoneNumber => _formatPhoneNumber(_phoneNumber);
  List<ContactModel> get contacts => _contacts;
  List<ContactModel> get filteredContacts => _filteredContacts;
  ContactModel? get matchedContact => _matchedContact;
  bool get isLoading => _isLoading;

  DialerProvider() {
    _loadContacts();
  }

  void addDigit(String digit) {
    if (_phoneNumber.length < 15) { // Reasonable limit
      _phoneNumber += digit;
      _findMatchingContact();
      _filterContacts();
      notifyListeners();
    }
  }

  void deleteDigit() {
    if (_phoneNumber.isNotEmpty) {
      _phoneNumber = _phoneNumber.substring(0, _phoneNumber.length - 1);
      _findMatchingContact();
      _filterContacts();
      notifyListeners();
    }
  }

  void clearNumber() {
    _phoneNumber = '';
    _matchedContact = null;
    _filteredContacts.clear();
    notifyListeners();
  }

  void setPhoneNumber(String number) {
    _phoneNumber = number;
    _findMatchingContact();
    _filterContacts();
    notifyListeners();
  }

  String _formatPhoneNumber(String number) {
    if (number.isEmpty) return '';
    
    // Basic formatting for display
    String formatted = number;
    if (number.length >= 10) {
      // Format as (XXX) XXX-XXXX for US numbers
      if (number.length == 10) {
        formatted = '(${number.substring(0, 3)}) ${number.substring(3, 6)}-${number.substring(6)}';
      } else if (number.length == 11 && number.startsWith('1')) {
        formatted = '+1 (${number.substring(1, 4)}) ${number.substring(4, 7)}-${number.substring(7)}';
      }
    }
    return formatted;
  }

  Future<void> _loadContacts() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Check permission
      if (await Permission.contacts.request().isGranted) {
        _contacts = await _contactService.getAllContacts();
      }
    } catch (e) {
      debugPrint('Error loading contacts: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _findMatchingContact() {
    if (_phoneNumber.isEmpty) {
      _matchedContact = null;
      return;
    }

    // Find exact match
    for (final contact in _contacts) {
      for (final phone in contact.phoneNumbers) {
        final cleanPhone = _cleanPhoneNumber(phone.number);
        final cleanInput = _cleanPhoneNumber(_phoneNumber);
        if (cleanPhone == cleanInput || cleanPhone.endsWith(cleanInput)) {
          _matchedContact = contact;
          return;
        }
      }
    }
    _matchedContact = null;
  }

  void _filterContacts() {
    if (_phoneNumber.isEmpty) {
      _filteredContacts.clear();
      return;
    }

    _filteredContacts = _contacts.where((contact) {
      // Check if contact name contains the digits (T9 style)
      final nameMatch = _matchesT9(contact.displayName, _phoneNumber);
      
      // Check if any phone number starts with the entered digits
      final phoneMatch = contact.phoneNumbers.any((phone) {
        final cleanPhone = _cleanPhoneNumber(phone.number);
        final cleanInput = _cleanPhoneNumber(_phoneNumber);
        return cleanPhone.startsWith(cleanInput);
      });

      return nameMatch || phoneMatch;
    }).take(5).toList(); // Limit to 5 suggestions
  }

  String _cleanPhoneNumber(String phone) {
    return phone.replaceAll(RegExp(r'[^\d]'), '');
  }

  bool _matchesT9(String name, String digits) {
    // Simple T9 matching - can be enhanced
    final t9Map = {
      '2': 'abc', '3': 'def', '4': 'ghi', '5': 'jkl',
      '6': 'mno', '7': 'pqrs', '8': 'tuv', '9': 'wxyz'
    };

    final lowerName = name.toLowerCase();
    int nameIndex = 0;

    for (int i = 0; i < digits.length && nameIndex < lowerName.length; i++) {
      final digit = digits[i];
      final letters = t9Map[digit];
      if (letters == null) continue;

      bool found = false;
      while (nameIndex < lowerName.length) {
        if (letters.contains(lowerName[nameIndex])) {
          found = true;
          nameIndex++;
          break;
        }
        nameIndex++;
      }
      if (!found) return false;
    }

    return digits.isNotEmpty;
  }

  Future<void> logCall(String phoneNumber) async {
    try {
      final callLog = CallLogModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        number: phoneNumber,
        name: _matchedContact?.displayName,
        type: CallType.outgoing,
        timestamp: DateTime.now(),
        duration: 0, // Will be updated when call ends
        isAnswered: false, // Will be updated
      );

      await _localStorage.saveCallLog(callLog);
    } catch (e) {
      debugPrint('Error logging call: $e');
    }
  }

  Future<void> refreshContacts() async {
    await _loadContacts();
  }

  // Speed dial functionality
  List<ContactModel> _speedDialContacts = [];
  List<ContactModel> get speedDialContacts => _speedDialContacts;

  Future<void> loadSpeedDial() async {
    try {
      _speedDialContacts = await _localStorage.getSpeedDialContacts();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading speed dial: $e');
    }
  }

  Future<void> addToSpeedDial(ContactModel contact, int position) async {
    try {
      await _localStorage.saveSpeedDialContact(contact, position);
      await loadSpeedDial();
    } catch (e) {
      debugPrint('Error adding to speed dial: $e');
    }
  }

  Future<void> removeFromSpeedDial(int position) async {
    try {
      await _localStorage.removeSpeedDialContact(position);
      await loadSpeedDial();
    } catch (e) {
      debugPrint('Error removing from speed dial: $e');
    }
  }
}
