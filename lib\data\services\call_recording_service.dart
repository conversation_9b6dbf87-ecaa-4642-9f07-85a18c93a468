import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CallRecordingService {
  static final CallRecordingService _instance = CallRecordingService._internal();
  factory CallRecordingService() => _instance;
  CallRecordingService._internal();

  final AudioRecorder _recorder = AudioRecorder();
  bool _isRecording = false;
  String? _currentRecordingPath;
  DateTime? _recordingStartTime;

  bool get isRecording => _isRecording;
  String? get currentRecordingPath => _currentRecordingPath;
  DateTime? get recordingStartTime => _recordingStartTime;

  Future<bool> isRecordingEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('call_recording_enabled') ?? false;
  }

  Future<void> setRecordingEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('call_recording_enabled', enabled);
  }

  Future<bool> checkPermissions() async {
    final microphonePermission = await Permission.microphone.status;
    final storagePermission = await Permission.storage.status;
    
    return microphonePermission.isGranted && storagePermission.isGranted;
  }

  Future<bool> requestPermissions() async {
    final permissions = [
      Permission.microphone,
      Permission.storage,
    ];

    final statuses = await permissions.request();
    return statuses.values.every((status) => status.isGranted);
  }

  Future<bool> startRecording(String callId, String phoneNumber) async {
    try {
      // Check if recording is enabled
      if (!await isRecordingEnabled()) {
        debugPrint('Call recording is disabled');
        return false;
      }

      // Check permissions
      if (!await checkPermissions()) {
        debugPrint('Recording permissions not granted');
        return false;
      }

      // Stop any existing recording
      if (_isRecording) {
        await stopRecording();
      }

      // Create recording directory
      final directory = await _getRecordingDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'call_${callId}_${timestamp}.m4a';
      _currentRecordingPath = '${directory.path}/$fileName';

      // Configure recording
      const config = RecordConfig(
        encoder: AudioEncoder.aacLc,
        bitRate: 128000,
        sampleRate: 44100,
      );

      // Start recording
      await _recorder.start(config, path: _currentRecordingPath!);
      
      _isRecording = true;
      _recordingStartTime = DateTime.now();

      debugPrint('Started recording call: $_currentRecordingPath');
      return true;
    } catch (e) {
      debugPrint('Error starting call recording: $e');
      return false;
    }
  }

  Future<String?> stopRecording() async {
    try {
      if (!_isRecording) {
        debugPrint('No recording in progress');
        return null;
      }

      final path = await _recorder.stop();
      _isRecording = false;
      
      final recordingPath = _currentRecordingPath;
      _currentRecordingPath = null;
      _recordingStartTime = null;

      debugPrint('Stopped recording call: $recordingPath');
      return recordingPath ?? path;
    } catch (e) {
      debugPrint('Error stopping call recording: $e');
      return null;
    }
  }

  Future<void> pauseRecording() async {
    try {
      if (_isRecording) {
        await _recorder.pause();
        debugPrint('Paused call recording');
      }
    } catch (e) {
      debugPrint('Error pausing call recording: $e');
    }
  }

  Future<void> resumeRecording() async {
    try {
      if (_isRecording) {
        await _recorder.resume();
        debugPrint('Resumed call recording');
      }
    } catch (e) {
      debugPrint('Error resuming call recording: $e');
    }
  }

  Future<Directory> _getRecordingDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final recordingDir = Directory('${appDir.path}/call_recordings');
    
    if (!await recordingDir.exists()) {
      await recordingDir.create(recursive: true);
    }
    
    return recordingDir;
  }

  Future<List<CallRecording>> getRecordings() async {
    try {
      final directory = await _getRecordingDirectory();
      final files = directory.listSync()
          .where((file) => file is File && file.path.endsWith('.m4a'))
          .cast<File>()
          .toList();

      final recordings = <CallRecording>[];
      for (final file in files) {
        final stat = await file.stat();
        final fileName = file.path.split('/').last;
        
        // Parse call ID from filename
        final parts = fileName.split('_');
        String? callId;
        if (parts.length >= 3) {
          callId = parts[1];
        }

        recordings.add(CallRecording(
          path: file.path,
          fileName: fileName,
          callId: callId,
          createdAt: stat.modified,
          size: stat.size,
        ));
      }

      // Sort by creation date (newest first)
      recordings.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return recordings;
    } catch (e) {
      debugPrint('Error getting recordings: $e');
      return [];
    }
  }

  Future<bool> deleteRecording(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        debugPrint('Deleted recording: $path');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting recording: $e');
      return false;
    }
  }

  Future<void> cleanupOldRecordings({int maxDays = 30}) async {
    try {
      final recordings = await getRecordings();
      final cutoffDate = DateTime.now().subtract(Duration(days: maxDays));

      for (final recording in recordings) {
        if (recording.createdAt.isBefore(cutoffDate)) {
          await deleteRecording(recording.path);
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up old recordings: $e');
    }
  }

  Future<int> getTotalRecordingsSize() async {
    try {
      final recordings = await getRecordings();
      return recordings.fold<int>(0, (sum, recording) => sum + recording.size);
    } catch (e) {
      debugPrint('Error calculating recordings size: $e');
      return 0;
    }
  }

  void dispose() {
    _recorder.dispose();
  }
}

class CallRecording {
  final String path;
  final String fileName;
  final String? callId;
  final DateTime createdAt;
  final int size;

  CallRecording({
    required this.path,
    required this.fileName,
    this.callId,
    required this.createdAt,
    required this.size,
  });

  String get formattedSize {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  Duration get estimatedDuration {
    // Rough estimation: 1MB ≈ 8 minutes for voice recording
    final minutes = (size / (1024 * 1024)) * 8;
    return Duration(minutes: minutes.round());
  }
}
