import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/dialer_provider.dart';
import '../../../data/models/contact_model.dart';

class ContactSuggestions extends StatelessWidget {
  const ContactSuggestions({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<DialerProvider>(
      builder: (context, dialerProvider, child) {
        if (dialerProvider.filteredContacts.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: dialerProvider.filteredContacts
                .map((contact) => _buildContactSuggestion(context, contact))
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildContactSuggestion(BuildContext context, ContactModel contact) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _selectContact(context, contact),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // Contact Avatar
              CircleAvatar(
                radius: 20,
                backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                child: contact.avatar != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Image.memory(
                          contact.avatar!,
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Text(
                        contact.displayName.isNotEmpty
                            ? contact.displayName[0].toUpperCase()
                            : '?',
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
              const SizedBox(width: 12),

              // Contact Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      contact.displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (contact.phoneNumbers.isNotEmpty)
                      Text(
                        contact.phoneNumbers.first.number,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),

              // Call Button
              IconButton(
                onPressed: () => _callContact(context, contact),
                icon: Icon(
                  Icons.call,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectContact(BuildContext context, ContactModel contact) {
    if (contact.phoneNumbers.isNotEmpty) {
      final phoneNumber = contact.phoneNumbers.first.number;
      context.read<DialerProvider>().setPhoneNumber(
        phoneNumber.replaceAll(RegExp(r'[^\d+]'), ''),
      );
    }
  }

  void _callContact(BuildContext context, ContactModel contact) {
    if (contact.phoneNumbers.isNotEmpty) {
      final phoneNumber = contact.phoneNumbers.first.number;
      context.read<DialerProvider>().setPhoneNumber(
        phoneNumber.replaceAll(RegExp(r'[^\d+]'), ''),
      );
      
      // Trigger call
      // This would typically be handled by the parent widget
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Calling ${contact.displayName}...'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
