import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SpeedDialSearchBar extends StatefulWidget {
  final String initialQuery;
  final Function(String) onSearchChanged;
  final VoidCallback? onClear;
  final bool isEnabled;
  final String hintText;

  const SpeedDialSearchBar({
    super.key,
    this.initialQuery = '',
    required this.onSearchChanged,
    this.onClear,
    this.isEnabled = true,
    this.hintText = 'Search speed dial contacts...',
  });

  @override
  State<SpeedDialSearchBar> createState() => _SpeedDialSearchBarState();
}

class _SpeedDialSearchBarState extends State<SpeedDialSearchBar>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  bool _isSearching = false;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    if (widget.initialQuery.isNotEmpty) {
      _isSearching = true;
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: _hasFocus ? 0.15 : 0.08),
                    blurRadius: _hasFocus ? 12 : 8,
                    offset: Offset(0, _hasFocus ? 4 : 2),
                  ),
                ],
                border: Border.all(
                  color: _hasFocus 
                      ? Theme.of(context).primaryColor 
                      : Colors.grey.shade300,
                  width: _hasFocus ? 2 : 1,
                ),
              ),
              child: TextField(
                controller: _controller,
                enabled: widget.isEnabled,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 14,
                  ),
                  prefixIcon: AnimatedBuilder(
                    animation: _fadeAnimation,
                    builder: (context, child) {
                      return Icon(
                        _isSearching ? Icons.search : Icons.search_outlined,
                        color: _hasFocus 
                            ? Theme.of(context).primaryColor 
                            : Colors.grey.shade400,
                        size: 20,
                      );
                    },
                  ),
                  suffixIcon: AnimatedBuilder(
                    animation: _fadeAnimation,
                    builder: (context, child) {
                      if (!_isSearching) return const SizedBox.shrink();
                      
                      return FadeTransition(
                        opacity: _fadeAnimation,
                        child: ScaleTransition(
                          scale: _scaleAnimation,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Search results count (if applicable)
                              if (_controller.text.isNotEmpty)
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    'Searching...',
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              
                              // Clear button
                              IconButton(
                                onPressed: _clearSearch,
                                icon: Icon(
                                  Icons.clear,
                                  color: Colors.grey.shade600,
                                  size: 18,
                                ),
                                padding: const EdgeInsets.all(4),
                                constraints: const BoxConstraints(
                                  minWidth: 32,
                                  minHeight: 32,
                                ),
                                tooltip: 'Clear search',
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textInputAction: TextInputAction.search,
                onChanged: _onSearchChanged,
                onSubmitted: _onSearchSubmitted,
                onTap: _onTap,
                onEditingComplete: _onEditingComplete,
              ),
            ),
          ),
          
          // Voice search button (future enhancement)
          const SizedBox(width: 8),
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: _hasFocus ? 48 : 0,
            child: _hasFocus
                ? Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: IconButton(
                      onPressed: _onVoiceSearch,
                      icon: Icon(
                        Icons.mic_outlined,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                      tooltip: 'Voice search',
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  void _onSearchChanged(String value) {
    setState(() {
      _isSearching = value.isNotEmpty;
    });
    
    if (value.isNotEmpty && !_animationController.isCompleted) {
      _animationController.forward();
    } else if (value.isEmpty && _animationController.isCompleted) {
      _animationController.reverse();
    }
    
    widget.onSearchChanged(value);
  }

  void _onSearchSubmitted(String value) {
    // Add haptic feedback
    HapticFeedback.lightImpact();
    
    // Unfocus the text field
    FocusScope.of(context).unfocus();
    
    widget.onSearchChanged(value);
  }

  void _onTap() {
    setState(() {
      _hasFocus = true;
    });
  }

  void _onEditingComplete() {
    setState(() {
      _hasFocus = false;
    });
  }

  void _clearSearch() {
    // Add haptic feedback
    HapticFeedback.lightImpact();
    
    _controller.clear();
    setState(() {
      _isSearching = false;
      _hasFocus = false;
    });
    
    _animationController.reverse();
    
    if (widget.onClear != null) {
      widget.onClear!();
    } else {
      widget.onSearchChanged('');
    }
    
    // Unfocus the text field
    FocusScope.of(context).unfocus();
  }

  void _onVoiceSearch() {
    // Add haptic feedback
    HapticFeedback.mediumImpact();
    
    // TODO: Implement voice search functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Voice search coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}

class SearchSuggestions extends StatelessWidget {
  final List<String> suggestions;
  final Function(String) onSuggestionTap;
  final bool isVisible;

  const SearchSuggestions({
    super.key,
    required this.suggestions,
    required this.onSuggestionTap,
    this.isVisible = false,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible || suggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: suggestions.take(5).map((suggestion) {
          return ListTile(
            dense: true,
            leading: Icon(
              Icons.search,
              color: Colors.grey.shade400,
              size: 18,
            ),
            title: Text(
              suggestion,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            onTap: () => onSuggestionTap(suggestion),
            trailing: Icon(
              Icons.north_west,
              color: Colors.grey.shade400,
              size: 16,
            ),
          );
        }).toList(),
      ),
    );
  }
}

class SearchFilters extends StatelessWidget {
  final List<String> activeFilters;
  final Function(String) onFilterToggle;
  final VoidCallback onClearAll;

  const SearchFilters({
    super.key,
    required this.activeFilters,
    required this.onFilterToggle,
    required this.onClearAll,
  });

  @override
  Widget build(BuildContext context) {
    final availableFilters = ['Favorites', 'Recent', 'Frequent', 'All'];
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: availableFilters.map((filter) {
                  final isActive = activeFilters.contains(filter);
                  
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(
                        filter,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: isActive ? Colors.white : Colors.grey.shade700,
                        ),
                      ),
                      selected: isActive,
                      onSelected: (_) => onFilterToggle(filter),
                      backgroundColor: Colors.grey.shade100,
                      selectedColor: Theme.of(context).primaryColor,
                      checkmarkColor: Colors.white,
                      side: BorderSide(
                        color: isActive 
                            ? Theme.of(context).primaryColor 
                            : Colors.grey.shade300,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          if (activeFilters.isNotEmpty)
            TextButton(
              onPressed: onClearAll,
              child: Text(
                'Clear',
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
