import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:vibration/vibration.dart';
import '../../providers/dialer_provider.dart';

class NumberPad extends StatelessWidget {
  const NumberPad({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: <PERSON>umn(
        children: [
          // Row 1: 1, 2, 3
          Row(
            children: [
              _buildNumberButton(context, '1', ''),
              _buildNumberButton(context, '2', 'ABC'),
              _buildNumberButton(context, '3', 'DEF'),
            ],
          ),
          const SizedBox(height: 15),

          // Row 2: 4, 5, 6
          Row(
            children: [
              _buildNumberButton(context, '4', 'GHI'),
              _buildNumberButton(context, '5', 'JKL'),
              _buildNumberButton(context, '6', 'MNO'),
            ],
          ),
          const SizedBox(height: 15),

          // Row 3: 7, 8, 9
          Row(
            children: [
              _buildNumberButton(context, '7', 'PQRS'),
              _buildNumberButton(context, '8', 'TUV'),
              _buildNumberButton(context, '9', 'WXYZ'),
            ],
          ),
          const SizedBox(height: 15),

          // Row 4: *, 0, #
          Row(
            children: [
              _buildNumberButton(context, '*', ''),
              _buildNumberButton(context, '0', '+'),
              _buildNumberButton(context, '#', ''),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNumberButton(
      BuildContext context, String number, String letters) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(35),
            onTap: () => _onNumberTap(context, number),
            onLongPress: number == '0' ? () => _onZeroLongPress(context) : null,
            child: Container(
              height: 70,
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(35),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    number,
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                  if (letters.isNotEmpty)
                    Text(
                      letters,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        color: Colors.grey[600],
                        letterSpacing: 1,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onNumberTap(BuildContext context, String number) async {
    // Get provider reference before async operations
    final dialerProvider = context.read<DialerProvider>();

    // Add haptic feedback
    HapticFeedback.lightImpact();

    // Add vibration if available
    final hasVibrator = await Vibration.hasVibrator();
    if (hasVibrator == true) {
      Vibration.vibrate(duration: 50);
    }

    // Add number to dialer
    dialerProvider.addDigit(number);
  }

  void _onZeroLongPress(BuildContext context) async {
    // Add haptic feedback
    HapticFeedback.mediumImpact();

    // Get provider reference before async operations
    final dialerProvider = context.read<DialerProvider>();

    // Add vibration if available
    final hasVibrator = await Vibration.hasVibrator();
    if (hasVibrator == true) {
      Vibration.vibrate(duration: 100);
    }

    // Add + symbol for international calls
    if (dialerProvider.phoneNumber.isEmpty) {
      dialerProvider.addDigit('+');
    }
  }
}
