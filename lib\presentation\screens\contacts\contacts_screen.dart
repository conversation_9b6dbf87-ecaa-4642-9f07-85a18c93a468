import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vibration/vibration.dart';
import '../../providers/contact_provider.dart';
import '../../providers/enhanced_call_provider.dart';
import '../../widgets/contacts/contact_list_item.dart';
import '../../widgets/contacts/add_contact_dialog.dart';
import '../../../data/models/contact_model.dart';

class ContactsScreen extends StatefulWidget {
  const ContactsScreen({super.key});

  @override
  State<ContactsScreen> createState() => _ContactsScreenState();
}

class _ContactsScreenState extends State<ContactsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ContactProvider>().loadContacts();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Contacts',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: () => _showAddContactDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () => _showContactOptions(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search contacts...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                          context.read<ContactProvider>().searchContacts('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                context.read<ContactProvider>().searchContacts(value);
              },
            ),
          ),

          // Contact List
          Expanded(
            child: Consumer<ContactProvider>(
              builder: (context, contactProvider, child) {
                if (contactProvider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                final contacts = _searchQuery.isEmpty
                    ? contactProvider.contacts
                    : contactProvider.searchResults;

                if (contacts.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.contacts_outlined,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isEmpty
                              ? 'No contacts found'
                              : 'No contacts match your search',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _searchQuery.isEmpty
                              ? 'Add your first contact to get started'
                              : 'Try a different search term',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade500,
                          ),
                        ),
                        if (_searchQuery.isEmpty) ...[
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: () => _showAddContactDialog(context),
                            icon: const Icon(Icons.person_add),
                            label: const Text('Add Contact'),
                          ),
                        ],
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => contactProvider.refreshContacts(),
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: contacts.length,
                    itemBuilder: (context, index) {
                      final contact = contacts[index];
                      return ContactListItem(
                        contact: contact,
                        onTap: () => _showContactDetails(context, contact),
                        onCall: () => _callContact(context, contact),
                        onMessage: () => _messageContact(context, contact),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showAddContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddContactDialog(),
    );
  }

  void _showContactOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.import_contacts),
              title: const Text('Import Contacts'),
              onTap: () {
                Navigator.pop(context);
                context.read<ContactProvider>().importDeviceContacts();
              },
            ),
            ListTile(
              leading: const Icon(Icons.backup),
              title: const Text('Backup Contacts'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement backup
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Contact Settings'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to contact settings
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showContactDetails(BuildContext context, ContactModel contact) {
    // TODO: Navigate to contact details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Contact details for ${contact.displayName}'),
      ),
    );
  }

  Future<void> _callContact(BuildContext context, ContactModel contact) async {
    if (contact.phoneNumbers.isEmpty) {
      _showErrorSnackBar(
          'No phone number available for ${contact.displayName}');
      return;
    }

    try {
      // Add haptic feedback
      HapticFeedback.mediumImpact();

      // Add vibration if available
      final hasVibrator = await Vibration.hasVibrator();
      if (hasVibrator == true) {
        await Vibration.vibrate(duration: 100);
      }

      final phoneNumber = contact.phoneNumbers.first.number;
      final Uri callUri = Uri(scheme: 'tel', path: phoneNumber);

      if (await canLaunchUrl(callUri)) {
        await launchUrl(callUri);

        if (mounted) {
          // Update contact frequency
          await context
              .read<ContactProvider>()
              .updateContactFrequency(contact.id);

          _showSuccessSnackBar('Calling ${contact.displayName}...');
        }
      } else {
        _showErrorSnackBar('Could not launch phone dialer');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to make call: $e');
    }
  }

  void _messageContact(BuildContext context, ContactModel contact) {
    if (contact.phoneNumbers.isNotEmpty) {
      // TODO: Open SMS app
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Messaging ${contact.displayName}...'),
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }
}
