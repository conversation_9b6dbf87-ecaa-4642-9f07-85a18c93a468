import 'package:hive/hive.dart';

part 'call_log_model.g.dart';

@HiveType(typeId: 3)
enum CallClassification {
  @HiveField(0)
  personal,
  @HiveField(1)
  business,
}

@HiveType(typeId: 4)
class CallLogModel {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String? name;

  @HiveField(2)
  final String number;

  @HiveField(3)
  final CallType type;

  @HiveField(4)
  final DateTime timestamp;

  @HiveField(5)
  final int duration;

  @HiveField(6)
  final bool isSynced;

  @HiveField(7)
  final String? leadId;

  @HiveField(8)
  final bool isAnswered;

  @HiveField(9)
  final CallClassification? classification;

  @HiveField(10)
  final DateTime? classificationDate;

  CallLogModel({
    required this.id,
    this.name,
    required this.number,
    required this.type,
    required this.timestamp,
    required this.duration,
    this.isSynced = false,
    this.leadId,
    this.isAnswered = false,
    this.classification,
    this.classificationDate,
  });

  factory CallLogModel.fromJson(Map<String, dynamic> json) {
    return CallLogModel(
      id: json['id'] as String,
      name: json['name'] as String?,
      number: json['number'] as String,
      type: CallType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CallType.outgoing,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      duration: json['duration'] as int,
      isSynced: json['is_synced'] as bool? ?? false,
      leadId: json['lead_id'] as String?,
      isAnswered: json['is_answered'] as bool? ?? false,
      classification: json['classification'] != null
          ? CallClassification.values.firstWhere(
              (e) => e.name == json['classification'],
              orElse: () => CallClassification.personal,
            )
          : null,
      classificationDate: json['classification_date'] != null
          ? DateTime.parse(json['classification_date'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'number': number,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
      'duration': duration,
      'is_synced': isSynced,
      'lead_id': leadId,
      'is_answered': isAnswered,
      'classification': classification?.name,
      'classification_date': classificationDate?.toIso8601String(),
    };
  }

  CallLogModel copyWith({
    String? id,
    String? name,
    String? number,
    CallType? type,
    DateTime? timestamp,
    int? duration,
    bool? isSynced,
    String? leadId,
    bool? isAnswered,
    CallClassification? classification,
    DateTime? classificationDate,
  }) {
    return CallLogModel(
      id: id ?? this.id,
      name: name ?? this.name,
      number: number ?? this.number,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      duration: duration ?? this.duration,
      isSynced: isSynced ?? this.isSynced,
      leadId: leadId ?? this.leadId,
      isAnswered: isAnswered ?? this.isAnswered,
      classification: classification ?? this.classification,
      classificationDate: classificationDate ?? this.classificationDate,
    );
  }
}

@HiveType(typeId: 9)
enum CallType {
  @HiveField(0)
  incoming,

  @HiveField(1)
  outgoing,

  @HiveField(2)
  missed,

  @HiveField(3)
  rejected,
}
