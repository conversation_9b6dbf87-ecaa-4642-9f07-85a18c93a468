import 'package:hive/hive.dart';

part 'lead_model.g.dart';

@HiveType(typeId: 1)
class LeadModel {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String phone;

  @HiveField(3)
  final String? email;

  @HiveField(4)
  final LeadStatus status;

  @HiveField(5)
  final String? notes;

  @HiveField(6)
  final DateTime? followUpDate;

  @HiveField(7)
  final DateTime createdAt;

  @HiveField(8)
  final DateTime updatedAt;

  @HiveField(9)
  final String? source;

  @HiveField(10)
  final double? estimatedValue;

  @HiveField(11)
  final bool isSynced;

  LeadModel({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    required this.status,
    this.notes,
    this.followUpDate,
    required this.createdAt,
    required this.updatedAt,
    this.source,
    this.estimatedValue,
    this.isSynced = true, // Default to true for server leads
  });

  factory LeadModel.fromJson(Map<String, dynamic> json) {
    return LeadModel(
      id: json['id'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String?,
      status: LeadStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => LeadStatus.new_lead,
      ),
      notes: json['notes'] as String?,
      followUpDate: json['follow_up_date'] != null
          ? DateTime.parse(json['follow_up_date'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      source: json['source'] as String?,
      estimatedValue: json['estimated_value']?.toDouble(),
      isSynced:
          json['is_synced'] as bool? ?? true, // Default to true for server data
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'status': status.name,
      'notes': notes,
      'follow_up_date': followUpDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'source': source,
      'estimated_value': estimatedValue,
      'is_synced': isSynced,
    };
  }

  LeadModel copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    LeadStatus? status,
    String? notes,
    DateTime? followUpDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? source,
    double? estimatedValue,
    bool? isSynced,
  }) {
    return LeadModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      followUpDate: followUpDate ?? this.followUpDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      source: source ?? this.source,
      estimatedValue: estimatedValue ?? this.estimatedValue,
      isSynced: isSynced ?? this.isSynced,
    );
  }
}

@HiveType(typeId: 2)
enum LeadStatus {
  @HiveField(0)
  new_lead,

  @HiveField(1)
  contacted,

  @HiveField(2)
  qualified,

  @HiveField(3)
  proposal,

  @HiveField(4)
  negotiation,

  @HiveField(5)
  closed_won,

  @HiveField(6)
  closed_lost,

  @HiveField(7)
  follow_up,
}
