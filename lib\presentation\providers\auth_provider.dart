import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../../data/models/user_model.dart';
import '../../data/services/api_service.dart';
import '../../data/services/local_storage_service.dart';

class AuthProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final LocalStorageService _localStorage = LocalStorageService();

  UserModel? _user;
  bool _isLoading = false;
  bool _isAuthenticated = false;

  UserModel? get user => _user;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;

  AuthProvider() {
    _initAuth();
  }

  Future<void> _initAuth() async {
    _isLoading = true;
    notifyListeners();

    try {
      final user = await _localStorage.getUser();
      if (user != null) {
        _user = user;
        _isAuthenticated = true;
      }
    } catch (e) {
      debugPrint('Error initializing auth: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    // Hardcoded credentials for now
    if (email == '<EMAIL>' && password == '123456') {
      final user = UserModel(
        id: '1',
        email: '<EMAIL>',
        name: 'Rupesh',
        token: 'dummy-token',
        createdAt: DateTime.now(),
      );
      await _localStorage.saveUser(user);
      _user = user;
      _isAuthenticated = true;
      _isLoading = false;
      notifyListeners();
      return true;
    }

    try {
      final user = await _apiService.login(email, password);
      await _localStorage.saveUser(user);

      _user = user;
      _isAuthenticated = true;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Login error: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> logout() async {
    try {
      // Try to logout from API, but don't let it block local logout
      try {
        await _apiService.logout();
      } catch (apiError) {
        debugPrint(
            'API logout failed (continuing with local logout): $apiError');
      }

      // Always clear local data regardless of API response
      await _localStorage.clearUser();

      _user = null;
      _isAuthenticated = false;
      notifyListeners();

      // Small delay to ensure UI updates
      await Future.delayed(const Duration(milliseconds: 100));

      debugPrint('Logout completed successfully');
    } catch (e) {
      debugPrint('Critical logout error: $e');
      // Even if there's an error, try to clear local state
      _user = null;
      _isAuthenticated = false;
      notifyListeners();
    }
  }
}
