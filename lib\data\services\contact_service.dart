import 'package:flutter/foundation.dart';
import 'package:flutter_contacts/flutter_contacts.dart' as fc;
import 'package:permission_handler/permission_handler.dart';
import '../models/contact_model.dart';

class ContactService {
  static final ContactService _instance = ContactService._internal();
  factory ContactService() => _instance;
  ContactService._internal();

  Future<List<ContactModel>> getAllContacts() async {
    try {
      // Check and request permission
      final permission = await Permission.contacts.status;
      if (!permission.isGranted) {
        final result = await Permission.contacts.request();
        if (!result.isGranted) {
          throw Exception('Contacts permission denied');
        }
      }

      // Get contacts from device
      final contacts = await fc.FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: true,
      );

      // Convert to our model
      return contacts.map(_convertToContactModel).toList();
    } catch (e) {
      debugPrint('Error getting contacts: $e');
      return [];
    }
  }

  Future<ContactModel?> getContactById(String id) async {
    try {
      final contact = await fc.FlutterContacts.getContact(id);
      if (contact == null) return null;
      return _convertToContactModel(contact);
    } catch (e) {
      debugPrint('Error getting contact by ID: $e');
      return null;
    }
  }

  Future<List<ContactModel>> searchContacts(String query) async {
    try {
      if (query.isEmpty) return [];

      final allContacts = await getAllContacts();
      return allContacts.where((contact) {
        final nameMatch = contact.displayName
            .toLowerCase()
            .contains(query.toLowerCase());
        final phoneMatch = contact.phoneNumbers.any((phone) =>
            phone.number.replaceAll(RegExp(r'[^\d]'), '').contains(query));
        return nameMatch || phoneMatch;
      }).toList();
    } catch (e) {
      debugPrint('Error searching contacts: $e');
      return [];
    }
  }

  Future<ContactModel> createContact({
    required String displayName,
    String? givenName,
    String? familyName,
    List<PhoneNumber>? phoneNumbers,
    List<EmailAddress>? emailAddresses,
  }) async {
    try {
      final contact = fc.Contact()
        ..displayName = displayName
        ..name = fc.Name(
          first: givenName ?? '',
          last: familyName ?? '',
        );

      // Add phone numbers
      if (phoneNumbers != null) {
        contact.phones = phoneNumbers
            .map((phone) => fc.Phone(
                  phone.number,
                  label: _getPhoneLabel(phone.label),
                ))
            .toList();
      }

      // Add email addresses
      if (emailAddresses != null) {
        contact.emails = emailAddresses
            .map((email) => fc.Email(
                  email.address,
                  label: _getEmailLabel(email.label),
                ))
            .toList();
      }

      // Insert contact
      await contact.insert();

      return _convertToContactModel(contact);
    } catch (e) {
      debugPrint('Error creating contact: $e');
      rethrow;
    }
  }

  Future<ContactModel> updateContact(ContactModel contact) async {
    try {
      final fcContact = await fc.FlutterContacts.getContact(contact.id);
      if (fcContact == null) {
        throw Exception('Contact not found');
      }

      // Update contact properties
      fcContact.displayName = contact.displayName;
      fcContact.name = fc.Name(
        first: contact.givenName ?? '',
        last: contact.familyName ?? '',
      );

      // Update phone numbers
      fcContact.phones = contact.phoneNumbers
          .map((phone) => fc.Phone(
                phone.number,
                label: _getPhoneLabel(phone.label),
              ))
          .toList();

      // Update email addresses
      fcContact.emails = contact.emailAddresses
          .map((email) => fc.Email(
                email.address,
                label: _getEmailLabel(email.label),
              ))
          .toList();

      // Update contact
      await fcContact.update();

      return _convertToContactModel(fcContact);
    } catch (e) {
      debugPrint('Error updating contact: $e');
      rethrow;
    }
  }

  Future<void> deleteContact(String contactId) async {
    try {
      final contact = await fc.FlutterContacts.getContact(contactId);
      if (contact != null) {
        await contact.delete();
      }
    } catch (e) {
      debugPrint('Error deleting contact: $e');
      rethrow;
    }
  }

  ContactModel _convertToContactModel(fc.Contact contact) {
    return ContactModel(
      id: contact.id,
      displayName: contact.displayName,
      givenName: contact.name.first,
      familyName: contact.name.last,
      phoneNumbers: contact.phones
          .map((phone) => PhoneNumber(
                number: phone.number,
                label: phone.label.name,
              ))
          .toList(),
      emailAddresses: contact.emails
          .map((email) => EmailAddress(
                address: email.address,
                label: email.label.name,
              ))
          .toList(),
      avatar: contact.photo,
    );
  }

  fc.PhoneLabel _getPhoneLabel(String label) {
    switch (label.toLowerCase()) {
      case 'mobile':
        return fc.PhoneLabel.mobile;
      case 'home':
        return fc.PhoneLabel.home;
      case 'work':
        return fc.PhoneLabel.work;
      case 'main':
        return fc.PhoneLabel.main;
      case 'fax':
        return fc.PhoneLabel.faxWork;
      default:
        return fc.PhoneLabel.mobile;
    }
  }

  fc.EmailLabel _getEmailLabel(String label) {
    switch (label.toLowerCase()) {
      case 'home':
        return fc.EmailLabel.home;
      case 'work':
        return fc.EmailLabel.work;
      case 'personal':
        return fc.EmailLabel.home;
      default:
        return fc.EmailLabel.home;
    }
  }
}
