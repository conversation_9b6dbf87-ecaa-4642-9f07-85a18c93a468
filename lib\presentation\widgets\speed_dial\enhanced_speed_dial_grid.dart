import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vibration/vibration.dart';
import '../../../data/models/contact_model.dart';

class EnhancedSpeedDialGrid extends StatefulWidget {
  final Map<int, ContactModel?> speedDialContacts;
  final bool isEditMode;
  final Function(ContactModel?, int) onContactTap;
  final Function(int) onEmptySlotTap;
  final Function(int) onRemoveContact;
  final Function(ContactModel, int) onContactLongPress;
  final Function(int, int) onReorder;
  final String searchQuery;

  const EnhancedSpeedDialGrid({
    super.key,
    required this.speedDialContacts,
    required this.isEditMode,
    required this.onContactTap,
    required this.onEmptySlotTap,
    required this.onRemoveContact,
    required this.onContactLongPress,
    required this.onReorder,
    this.searchQuery = '',
  });

  @override
  State<EnhancedSpeedDialGrid> createState() => _EnhancedSpeedDialGridState();
}

class _EnhancedSpeedDialGridState extends State<EnhancedSpeedDialGrid>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  int? _draggedPosition;
  int? _hoveredPosition;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredContacts = _getFilteredContacts();
    
    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount = constraints.maxWidth > 600 ? 4 : 3;
        final childAspectRatio = constraints.maxWidth > 600 ? 1.1 : 1.0;
        
        return ReorderableGridView.count(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: childAspectRatio,
          onReorder: widget.isEditMode ? _handleReorder : null,
          children: List.generate(9, (index) {
            final position = index + 1;
            final contact = widget.speedDialContacts[position];
            final isVisible = _isContactVisible(contact, position);
            
            if (!isVisible && widget.searchQuery.isNotEmpty) {
              return const SizedBox.shrink();
            }
            
            return _buildSpeedDialItem(
              context,
              position,
              contact,
              key: ValueKey(position),
            );
          }).where((widget) => widget is! SizedBox).toList(),
        );
      },
    );
  }

  Map<int, ContactModel?> _getFilteredContacts() {
    if (widget.searchQuery.isEmpty) return widget.speedDialContacts;
    
    final filtered = <int, ContactModel?>{};
    for (final entry in widget.speedDialContacts.entries) {
      if (_isContactVisible(entry.value, entry.key)) {
        filtered[entry.key] = entry.value;
      }
    }
    return filtered;
  }

  bool _isContactVisible(ContactModel? contact, int position) {
    if (widget.searchQuery.isEmpty) return true;
    if (contact == null) return true; // Show empty slots
    
    final query = widget.searchQuery.toLowerCase();
    final nameMatch = contact.displayName.toLowerCase().contains(query);
    final phoneMatch = contact.phoneNumbers.any((phone) =>
        phone.number.replaceAll(RegExp(r'[^\d]'), '').contains(query));
    
    return nameMatch || phoneMatch;
  }

  Widget _buildSpeedDialItem(
    BuildContext context,
    int position,
    ContactModel? contact, {
    required Key key,
  }) {
    final isEmpty = contact == null;
    final isHovered = _hoveredPosition == position;
    final isDragged = _draggedPosition == position;
    
    return AnimatedBuilder(
      key: key,
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isDragged ? 1.1 : (isHovered ? 1.02 : 1.0),
          child: GestureDetector(
            onTap: () => _handleTap(position, contact),
            onLongPress: () => _handleLongPress(position, contact),
            child: MouseRegion(
              onEnter: (_) => setState(() => _hoveredPosition = position),
              onExit: (_) => setState(() => _hoveredPosition = null),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: isHovered ? 0.15 : 0.08),
                      blurRadius: isHovered ? 12 : 8,
                      offset: Offset(0, isHovered ? 4 : 2),
                    ),
                  ],
                  border: _getBorder(position, contact),
                ),
                child: Stack(
                  children: [
                    // Main content
                    _buildMainContent(position, contact),
                    
                    // Edit mode overlay
                    if (widget.isEditMode && !isEmpty)
                      _buildEditOverlay(position),
                    
                    // Call indicator for non-edit mode
                    if (!widget.isEditMode && !isEmpty)
                      _buildCallIndicator(),
                    
                    // Loading indicator
                    if (isDragged)
                      _buildLoadingIndicator(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Border? _getBorder(int position, ContactModel? contact) {
    if (widget.isEditMode && contact != null) {
      return Border.all(color: Colors.blue, width: 2);
    }
    if (_hoveredPosition == position) {
      return Border.all(color: Theme.of(context).primaryColor, width: 1);
    }
    return null;
  }

  Widget _buildMainContent(int position, ContactModel? contact) {
    final isEmpty = contact == null;
    
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Position number
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isEmpty 
                  ? Colors.grey.shade300 
                  : Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                position.toString(),
                style: TextStyle(
                  color: isEmpty ? Colors.grey.shade600 : Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          
          // Contact avatar or add icon
          if (isEmpty) ...[
            _buildEmptySlot(),
          ] else ...[
            _buildContactContent(contact!),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptySlot() {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.grey.shade300,
              style: BorderStyle.solid,
              width: 2,
            ),
          ),
          child: Icon(
            Icons.add,
            color: Colors.grey.shade400,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Add Contact',
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildContactContent(ContactModel contact) {
    return Column(
      children: [
        // Contact avatar
        Stack(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                gradient: LinearGradient(
                  colors: _getAvatarColors(contact.displayName),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: contact.avatar != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(25),
                      child: Image.memory(
                        contact.avatar!,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Center(
                      child: Text(
                        _getInitials(contact.displayName),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
            ),
            // Favorite indicator
            if (contact.isFavorite)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.amber.shade600,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: const Icon(
                    Icons.star,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Contact name
        Text(
          contact.displayName,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        
        // Phone number
        if (contact.phoneNumbers.isNotEmpty)
          Text(
            _formatPhoneNumber(contact.phoneNumbers.first.number),
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        
        // Last contacted indicator
        if (contact.lastContactedDate != null)
          Text(
            _getLastContactedText(contact.lastContactedDate!),
            style: TextStyle(
              fontSize: 9,
              color: Colors.grey.shade500,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
      ],
    );
  }

  Widget _buildEditOverlay(int position) {
    return Positioned(
      top: 8,
      right: 8,
      child: GestureDetector(
        onTap: () => _handleRemove(position),
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withValues(alpha: 0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Icon(
            Icons.close,
            color: Colors.white,
            size: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildCallIndicator() {
    return Positioned(
      bottom: 8,
      right: 8,
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: Colors.green,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.green.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Icon(
          Icons.call,
          color: Colors.white,
          size: 12,
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Center(
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );
  }

  void _handleTap(int position, ContactModel? contact) async {
    // Add haptic feedback
    HapticFeedback.lightImpact();
    
    // Add vibration if available
    final hasVibrator = await Vibration.hasVibrator();
    if (hasVibrator == true) {
      Vibration.vibrate(duration: 50);
    }

    if (contact == null) {
      widget.onEmptySlotTap(position);
    } else {
      widget.onContactTap(contact, position);
    }
  }

  void _handleLongPress(int position, ContactModel? contact) async {
    if (contact == null) return;
    
    // Add haptic feedback
    HapticFeedback.mediumImpact();
    
    // Add vibration if available
    final hasVibrator = await Vibration.hasVibrator();
    if (hasVibrator == true) {
      Vibration.vibrate(duration: 100);
    }

    widget.onContactLongPress(contact, position);
  }

  void _handleRemove(int position) async {
    // Add haptic feedback
    HapticFeedback.heavyImpact();
    
    widget.onRemoveContact(position);
  }

  void _handleReorder(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    
    final oldPosition = oldIndex + 1;
    final newPosition = newIndex + 1;
    
    widget.onReorder(oldPosition, newPosition);
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  List<Color> _getAvatarColors(String name) {
    final hash = name.hashCode;
    final colors = [
      [Colors.blue.shade400, Colors.blue.shade600],
      [Colors.green.shade400, Colors.green.shade600],
      [Colors.purple.shade400, Colors.purple.shade600],
      [Colors.orange.shade400, Colors.orange.shade600],
      [Colors.teal.shade400, Colors.teal.shade600],
      [Colors.pink.shade400, Colors.pink.shade600],
      [Colors.indigo.shade400, Colors.indigo.shade600],
      [Colors.red.shade400, Colors.red.shade600],
    ];
    
    return colors[hash.abs() % colors.length];
  }

  String _formatPhoneNumber(String number) {
    if (number.length >= 10) {
      return '(${number.substring(0, 3)}) ${number.substring(3, 6)}-${number.substring(6)}';
    }
    return number;
  }

  String _getLastContactedText(DateTime lastContacted) {
    final now = DateTime.now();
    final difference = now.difference(lastContacted);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

// Custom ReorderableGridView implementation
class ReorderableGridView extends StatelessWidget {
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final double childAspectRatio;
  final Function(int, int)? onReorder;
  final List<Widget> children;

  const ReorderableGridView.count({
    super.key,
    required this.crossAxisCount,
    required this.crossAxisSpacing,
    required this.mainAxisSpacing,
    required this.childAspectRatio,
    this.onReorder,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    if (onReorder == null) {
      return GridView.count(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
        childAspectRatio: childAspectRatio,
        children: children,
      );
    }

    return ReorderableListView(
      onReorder: onReorder!,
      children: children,
    );
  }
}
