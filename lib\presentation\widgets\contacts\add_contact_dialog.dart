import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/contact_provider.dart';
import '../../../data/models/contact_model.dart';

class AddContactDialog extends StatefulWidget {
  final ContactModel? contact; // For editing existing contact

  const AddContactDialog({super.key, this.contact});

  @override
  State<AddContactDialog> createState() => _AddContactDialogState();
}

class _AddContactDialogState extends State<AddContactDialog> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneControllers = <TextEditingController>[];
  final _emailControllers = <TextEditingController>[];
  
  final List<String> _phoneLabels = ['Mobile', 'Home', 'Work', 'Other'];
  final List<String> _emailLabels = ['Personal', 'Work', 'Other'];
  
  List<String> _selectedPhoneLabels = ['Mobile'];
  List<String> _selectedEmailLabels = ['Personal'];
  
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    if (widget.contact != null) {
      // Editing existing contact
      final contact = widget.contact!;
      _firstNameController.text = contact.givenName ?? '';
      _lastNameController.text = contact.familyName ?? '';
      
      // Initialize phone numbers
      for (int i = 0; i < contact.phoneNumbers.length; i++) {
        _phoneControllers.add(TextEditingController(
          text: contact.phoneNumbers[i].number,
        ));
        if (i < _selectedPhoneLabels.length) {
          _selectedPhoneLabels[i] = contact.phoneNumbers[i].label;
        } else {
          _selectedPhoneLabels.add(contact.phoneNumbers[i].label);
        }
      }
      
      // Initialize email addresses
      for (int i = 0; i < contact.emailAddresses.length; i++) {
        _emailControllers.add(TextEditingController(
          text: contact.emailAddresses[i].address,
        ));
        if (i < _selectedEmailLabels.length) {
          _selectedEmailLabels[i] = contact.emailAddresses[i].label;
        } else {
          _selectedEmailLabels.add(contact.emailAddresses[i].label);
        }
      }
    } else {
      // Adding new contact
      _phoneControllers.add(TextEditingController());
      _emailControllers.add(TextEditingController());
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    for (final controller in _phoneControllers) {
      controller.dispose();
    }
    for (final controller in _emailControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 600),
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Text(
                    widget.contact == null ? 'Add Contact' : 'Edit Contact',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Form Fields
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name Fields
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _firstNameController,
                              decoration: const InputDecoration(
                                labelText: 'First Name',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'First name is required';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: _lastNameController,
                              decoration: const InputDecoration(
                                labelText: 'Last Name',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Phone Numbers
                      _buildSectionHeader('Phone Numbers'),
                      ..._buildPhoneFields(),
                      _buildAddButton('Add Phone', _addPhoneField),
                      const SizedBox(height: 24),

                      // Email Addresses
                      _buildSectionHeader('Email Addresses'),
                      ..._buildEmailFields(),
                      _buildAddButton('Add Email', _addEmailField),
                    ],
                  ),
                ),
              ),

              // Action Buttons
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveContact,
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Text(widget.contact == null ? 'Add' : 'Save'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  List<Widget> _buildPhoneFields() {
    return List.generate(_phoneControllers.length, (index) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _phoneControllers[index],
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (value.length < 10) {
                      return 'Invalid phone number';
                    }
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: index < _selectedPhoneLabels.length 
                    ? _selectedPhoneLabels[index] 
                    : _phoneLabels.first,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
                items: _phoneLabels.map((label) {
                  return DropdownMenuItem(
                    value: label,
                    child: Text(label),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    if (index < _selectedPhoneLabels.length) {
                      _selectedPhoneLabels[index] = value!;
                    } else {
                      _selectedPhoneLabels.add(value!);
                    }
                  });
                },
              ),
            ),
            if (_phoneControllers.length > 1)
              IconButton(
                onPressed: () => _removePhoneField(index),
                icon: const Icon(Icons.remove_circle_outline),
                color: Colors.red,
              ),
          ],
        ),
      );
    });
  }

  List<Widget> _buildEmailFields() {
    return List.generate(_emailControllers.length, (index) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _emailControllers[index],
                decoration: const InputDecoration(
                  labelText: 'Email Address',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                      return 'Invalid email address';
                    }
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: index < _selectedEmailLabels.length 
                    ? _selectedEmailLabels[index] 
                    : _emailLabels.first,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
                items: _emailLabels.map((label) {
                  return DropdownMenuItem(
                    value: label,
                    child: Text(label),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    if (index < _selectedEmailLabels.length) {
                      _selectedEmailLabels[index] = value!;
                    } else {
                      _selectedEmailLabels.add(value!);
                    }
                  });
                },
              ),
            ),
            if (_emailControllers.length > 1)
              IconButton(
                onPressed: () => _removeEmailField(index),
                icon: const Icon(Icons.remove_circle_outline),
                color: Colors.red,
              ),
          ],
        ),
      );
    });
  }

  Widget _buildAddButton(String text, VoidCallback onPressed) {
    return TextButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.add),
      label: Text(text),
    );
  }

  void _addPhoneField() {
    setState(() {
      _phoneControllers.add(TextEditingController());
      _selectedPhoneLabels.add(_phoneLabels.first);
    });
  }

  void _removePhoneField(int index) {
    setState(() {
      _phoneControllers[index].dispose();
      _phoneControllers.removeAt(index);
      if (index < _selectedPhoneLabels.length) {
        _selectedPhoneLabels.removeAt(index);
      }
    });
  }

  void _addEmailField() {
    setState(() {
      _emailControllers.add(TextEditingController());
      _selectedEmailLabels.add(_emailLabels.first);
    });
  }

  void _removeEmailField(int index) {
    setState(() {
      _emailControllers[index].dispose();
      _emailControllers.removeAt(index);
      if (index < _selectedEmailLabels.length) {
        _selectedEmailLabels.removeAt(index);
      }
    });
  }

  void _saveContact() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final firstName = _firstNameController.text.trim();
      final lastName = _lastNameController.text.trim();
      final displayName = '$firstName ${lastName}'.trim();

      // Build phone numbers
      final phoneNumbers = <PhoneNumber>[];
      for (int i = 0; i < _phoneControllers.length; i++) {
        final number = _phoneControllers[i].text.trim();
        if (number.isNotEmpty) {
          phoneNumbers.add(PhoneNumber(
            number: number,
            label: i < _selectedPhoneLabels.length 
                ? _selectedPhoneLabels[i] 
                : _phoneLabels.first,
          ));
        }
      }

      // Build email addresses
      final emailAddresses = <EmailAddress>[];
      for (int i = 0; i < _emailControllers.length; i++) {
        final email = _emailControllers[i].text.trim();
        if (email.isNotEmpty) {
          emailAddresses.add(EmailAddress(
            address: email,
            label: i < _selectedEmailLabels.length 
                ? _selectedEmailLabels[i] 
                : _emailLabels.first,
          ));
        }
      }

      final contact = ContactModel(
        id: widget.contact?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
        displayName: displayName,
        givenName: firstName,
        familyName: lastName,
        phoneNumbers: phoneNumbers,
        emailAddresses: emailAddresses,
        isFavorite: widget.contact?.isFavorite ?? false,
        contactFrequency: widget.contact?.contactFrequency ?? 0,
        lastContactedDate: widget.contact?.lastContactedDate,
      );

      final contactProvider = context.read<ContactProvider>();
      if (widget.contact == null) {
        await contactProvider.addContact(contact);
      } else {
        await contactProvider.updateContact(contact);
      }

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.contact == null 
                  ? 'Contact added successfully' 
                  : 'Contact updated successfully',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
