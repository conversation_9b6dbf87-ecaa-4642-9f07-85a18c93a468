import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SpeedDialOptionsMenu extends StatelessWidget {
  final VoidCallback onClearAll;
  final VoidCallback onImportFavorites;
  final VoidCallback onExportSpeedDial;
  final VoidCallback onBackupSpeedDial;
  final VoidCallback onRestoreSpeedDial;
  final VoidCallback onSettings;
  final Map<String, dynamic>? stats;

  const SpeedDialOptionsMenu({
    super.key,
    required this.onClearAll,
    required this.onImportFavorites,
    required this.onExportSpeedDial,
    required this.onBackupSpeedDial,
    required this.onRestoreSpeedDial,
    required this.onSettings,
    this.stats,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: <PERSON>umn(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.settings,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Speed Dial Options',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Stats section
          if (stats != null) ...[
            _buildStatsSection(context),
            const SizedBox(height: 20),
          ],

          // Quick Actions
          _buildSectionHeader('Quick Actions'),
          const SizedBox(height: 12),
          _buildOptionTile(
            context,
            icon: Icons.star_border,
            title: 'Import Favorites',
            subtitle: 'Add favorite contacts to speed dial',
            onTap: () {
              Navigator.pop(context);
              onImportFavorites();
            },
            color: Colors.amber,
          ),
          _buildOptionTile(
            context,
            icon: Icons.clear_all,
            title: 'Clear All',
            subtitle: 'Remove all speed dial contacts',
            onTap: () {
              Navigator.pop(context);
              _showClearAllDialog(context);
            },
            color: Colors.red,
            isDestructive: true,
          ),

          const SizedBox(height: 20),

          // Data Management
          _buildSectionHeader('Data Management'),
          const SizedBox(height: 12),
          _buildOptionTile(
            context,
            icon: Icons.file_upload,
            title: 'Export Speed Dial',
            subtitle: 'Save speed dial to file',
            onTap: () {
              Navigator.pop(context);
              onExportSpeedDial();
            },
            color: Colors.blue,
          ),
          _buildOptionTile(
            context,
            icon: Icons.backup,
            title: 'Backup Speed Dial',
            subtitle: 'Save to cloud storage',
            onTap: () {
              Navigator.pop(context);
              onBackupSpeedDial();
            },
            color: Colors.green,
          ),
          _buildOptionTile(
            context,
            icon: Icons.restore,
            title: 'Restore Speed Dial',
            subtitle: 'Restore from backup',
            onTap: () {
              Navigator.pop(context);
              onRestoreSpeedDial();
            },
            color: Colors.orange,
          ),

          const SizedBox(height: 20),

          // Settings
          _buildSectionHeader('Settings'),
          const SizedBox(height: 12),
          _buildOptionTile(
            context,
            icon: Icons.settings_outlined,
            title: 'Speed Dial Settings',
            subtitle: 'Configure speed dial preferences',
            onTap: () {
              Navigator.pop(context);
              onSettings();
            },
            color: Colors.grey,
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildStatsSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Speed Dial Statistics',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Occupied',
                  '${stats!['occupied_slots']}/${stats!['total_slots']}',
                  Colors.blue,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Available',
                  '${stats!['available_slots']}',
                  Colors.green,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Usage',
                  '${stats!['usage_percentage']}%',
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: Colors.grey.shade700,
      ),
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            HapticFeedback.lightImpact();
            onTap();
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDestructive 
                    ? Colors.red.shade200 
                    : Colors.grey.shade200,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isDestructive ? Colors.red : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: Colors.grey.shade400,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.warning,
              color: Colors.red.shade600,
              size: 24,
            ),
            const SizedBox(width: 12),
            const Text('Clear All Speed Dial'),
          ],
        ),
        content: const Text(
          'Are you sure you want to remove all speed dial contacts? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onClearAll();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}

class SpeedDialContextMenu extends StatelessWidget {
  final ContactModel contact;
  final int position;
  final VoidCallback onCall;
  final VoidCallback onMessage;
  final VoidCallback onEdit;
  final VoidCallback onRemove;
  final VoidCallback onAddToFavorites;
  final VoidCallback onBlock;
  final VoidCallback onMovePosition;

  const SpeedDialContextMenu({
    super.key,
    required this.contact,
    required this.position,
    required this.onCall,
    required this.onMessage,
    required this.onEdit,
    required this.onRemove,
    required this.onAddToFavorites,
    required this.onBlock,
    required this.onMovePosition,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with contact info
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  gradient: LinearGradient(
                    colors: _getAvatarColors(contact.displayName),
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: contact.avatar != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(25),
                        child: Image.memory(
                          contact.avatar!,
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Center(
                        child: Text(
                          _getInitials(contact.displayName),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      contact.displayName,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (contact.phoneNumbers.isNotEmpty)
                      Text(
                        contact.phoneNumbers.first.number,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    Text(
                      'Speed Dial $position',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  context,
                  icon: Icons.call,
                  label: 'Call',
                  color: Colors.green,
                  onTap: () {
                    Navigator.pop(context);
                    onCall();
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  context,
                  icon: Icons.message,
                  label: 'Message',
                  color: Colors.blue,
                  onTap: () {
                    Navigator.pop(context);
                    onMessage();
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Menu options
          _buildMenuOption(
            context,
            icon: Icons.edit,
            title: 'Edit Contact',
            onTap: () {
              Navigator.pop(context);
              onEdit();
            },
          ),
          _buildMenuOption(
            context,
            icon: Icons.swap_horiz,
            title: 'Move Position',
            onTap: () {
              Navigator.pop(context);
              onMovePosition();
            },
          ),
          _buildMenuOption(
            context,
            icon: contact.isFavorite ? Icons.star : Icons.star_border,
            title: contact.isFavorite ? 'Remove from Favorites' : 'Add to Favorites',
            onTap: () {
              Navigator.pop(context);
              onAddToFavorites();
            },
          ),
          _buildMenuOption(
            context,
            icon: Icons.block,
            title: 'Block Number',
            onTap: () {
              Navigator.pop(context);
              onBlock();
            },
            isDestructive: true,
          ),
          _buildMenuOption(
            context,
            icon: Icons.remove_circle,
            title: 'Remove from Speed Dial',
            onTap: () {
              Navigator.pop(context);
              onRemove();
            },
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : Colors.grey.shade600,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? Colors.red : Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  List<Color> _getAvatarColors(String name) {
    final hash = name.hashCode;
    final colors = [
      [Colors.blue.shade400, Colors.blue.shade600],
      [Colors.green.shade400, Colors.green.shade600],
      [Colors.purple.shade400, Colors.purple.shade600],
      [Colors.orange.shade400, Colors.orange.shade600],
      [Colors.teal.shade400, Colors.teal.shade600],
      [Colors.pink.shade400, Colors.pink.shade600],
      [Colors.indigo.shade400, Colors.indigo.shade600],
      [Colors.red.shade400, Colors.red.shade600],
    ];
    
    return colors[hash.abs() % colors.length];
  }
}
