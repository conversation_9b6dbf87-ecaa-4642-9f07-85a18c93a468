import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  static const Color _primaryColor = Color(0xFF007BFF);
  static const Color _secondaryColor = Color(0xFF6C757D);
  static const Color _lightBackgroundColor = Color(0xFFF8F9FA);
  static const Color _darkBackgroundColor = Color(0xFF343A40);

  static final ThemeData lightTheme = ThemeData(
    primaryColor: _primaryColor,
    colorScheme: const ColorScheme.light(
      primary: _primaryColor,
      secondary: _secondaryColor,
      background: _lightBackgroundColor,
    ),
    scaffoldBackgroundColor: _lightBackgroundColor,
    textTheme: GoogleFonts.latoTextTheme(ThemeData.light().textTheme),
    visualDensity: VisualDensity.adaptivePlatformDensity,
  );

  static final ThemeData darkTheme = ThemeData(
    primaryColor: _primaryColor,
    colorScheme: const ColorScheme.dark(
      primary: _primaryColor,
      secondary: _secondaryColor,
      background: _darkBackgroundColor,
    ),
    scaffoldBackgroundColor: _darkBackgroundColor,
    textTheme: GoogleFonts.latoTextTheme(ThemeData.dark().textTheme),
    visualDensity: VisualDensity.adaptivePlatformDensity,
  );
}
