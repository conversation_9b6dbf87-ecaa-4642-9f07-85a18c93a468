import 'package:hive/hive.dart';

part 'blocked_number_model.g.dart';

@HiveType(typeId: 8)
class BlockedNumber {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String phoneNumber; // Cleaned phone number for matching

  @HiveField(2)
  final String originalNumber; // Original number as entered

  @HiveField(3)
  final String reason;

  @HiveField(4)
  final DateTime blockedAt;

  @HiveField(5)
  final int blockCount; // Number of times this number was blocked

  @HiveField(6)
  final DateTime? lastBlockedCall; // Last time a call from this number was blocked

  BlockedNumber({
    required this.id,
    required this.phoneNumber,
    required this.originalNumber,
    required this.reason,
    required this.blockedAt,
    this.blockCount = 0,
    this.lastBlockedCall,
  });

  factory BlockedNumber.fromJson(Map<String, dynamic> json) {
    return BlockedNumber(
      id: json['id'] as String,
      phoneNumber: json['phone_number'] as String,
      originalNumber: json['original_number'] as String,
      reason: json['reason'] as String,
      blockedAt: DateTime.parse(json['blocked_at'] as String),
      blockCount: json['block_count'] as int? ?? 0,
      lastBlockedCall: json['last_blocked_call'] != null
          ? DateTime.parse(json['last_blocked_call'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'phone_number': phoneNumber,
      'original_number': originalNumber,
      'reason': reason,
      'blocked_at': blockedAt.toIso8601String(),
      'block_count': blockCount,
      'last_blocked_call': lastBlockedCall?.toIso8601String(),
    };
  }

  BlockedNumber copyWith({
    String? id,
    String? phoneNumber,
    String? originalNumber,
    String? reason,
    DateTime? blockedAt,
    int? blockCount,
    DateTime? lastBlockedCall,
  }) {
    return BlockedNumber(
      id: id ?? this.id,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      originalNumber: originalNumber ?? this.originalNumber,
      reason: reason ?? this.reason,
      blockedAt: blockedAt ?? this.blockedAt,
      blockCount: blockCount ?? this.blockCount,
      lastBlockedCall: lastBlockedCall ?? this.lastBlockedCall,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BlockedNumber &&
        other.id == id &&
        other.phoneNumber == phoneNumber;
  }

  @override
  int get hashCode {
    return id.hashCode ^ phoneNumber.hashCode;
  }

  @override
  String toString() {
    return 'BlockedNumber(id: $id, phoneNumber: $phoneNumber, reason: $reason)';
  }
}
