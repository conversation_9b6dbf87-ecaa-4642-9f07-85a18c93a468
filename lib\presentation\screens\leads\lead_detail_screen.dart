import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../data/models/lead_model.dart';
import '../../providers/lead_provider.dart';

class LeadDetailScreen extends StatefulWidget {
  final LeadModel lead;

  const LeadDetailScreen({
    super.key,
    required this.lead,
  });

  @override
  State<LeadDetailScreen> createState() => _LeadDetailScreenState();
}

class _LeadDetailScreenState extends State<LeadDetailScreen> {
  late LeadModel _lead;
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _lead = widget.lead;
    _notesController.text = _lead.notes ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_lead.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showEditDialog(context),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'call':
                  _makeCall(_lead.phone);
                  break;
                case 'sms':
                  _sendSMS(_lead.phone);
                  break;
                case 'whatsapp':
                  _openWhatsApp(_lead.phone);
                  break;
                case 'email':
                  if (_lead.email != null) _sendEmail(_lead.email!);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'call',
                child: Row(
                  children: [
                    Icon(Icons.call),
                    SizedBox(width: 8),
                    Text('Call'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sms',
                child: Row(
                  children: [
                    Icon(Icons.sms),
                    SizedBox(width: 8),
                    Text('SMS'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'whatsapp',
                child: Row(
                  children: [
                    Icon(Icons.chat),
                    SizedBox(width: 8),
                    Text('WhatsApp'),
                  ],
                ),
              ),
              if (_lead.email != null)
                const PopupMenuItem(
                  value: 'email',
                  child: Row(
                    children: [
                      Icon(Icons.email),
                      SizedBox(width: 8),
                      Text('Email'),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Lead Info Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: _getStatusColor(_lead.status),
                          child: Text(
                            _lead.name.substring(0, 1).toUpperCase(),
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _lead.name,
                                style:
                                    Theme.of(context).textTheme.headlineSmall,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _lead.phone,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge
                                    ?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                              ),
                              if (_lead.email != null) ...[
                                const SizedBox(height: 2),
                                Text(
                                  _lead.email!,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Status
                    Row(
                      children: [
                        Icon(Icons.flag, color: Colors.grey[600]),
                        const SizedBox(width: 8),
                        Text(
                          'Status: ${_getStatusDisplayName(_lead.status)}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color:
                                _getStatusColor(_lead.status).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                                color: _getStatusColor(_lead.status)),
                          ),
                          child: Text(
                            _getStatusDisplayName(_lead.status),
                            style: TextStyle(
                              color: _getStatusColor(_lead.status),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Follow-up Date
                    if (_lead.followUpDate != null)
                      Row(
                        children: [
                          Icon(Icons.schedule, color: Colors.grey[600]),
                          const SizedBox(width: 8),
                          Text(
                            'Follow-up: ${_formatDate(_lead.followUpDate!)}',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),

                    const SizedBox(height: 12),

                    // Created Date
                    Row(
                      children: [
                        Icon(Icons.calendar_today, color: Colors.grey[600]),
                        const SizedBox(width: 8),
                        Text(
                          'Created: ${_formatDate(_lead.createdAt)}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Quick Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quick Actions',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _ActionButton(
                          icon: Icons.call,
                          label: 'Call',
                          onTap: () => _makeCall(_lead.phone),
                        ),
                        _ActionButton(
                          icon: Icons.sms,
                          label: 'SMS',
                          onTap: () => _sendSMS(_lead.phone),
                        ),
                        _ActionButton(
                          icon: Icons.chat,
                          label: 'WhatsApp',
                          onTap: () => _openWhatsApp(_lead.phone),
                        ),
                        if (_lead.email != null)
                          _ActionButton(
                            icon: Icons.email,
                            label: 'Email',
                            onTap: () => _sendEmail(_lead.email!),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Notes Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Notes',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const Spacer(),
                        TextButton(
                          onPressed: () => _showEditNotesDialog(context),
                          child: const Text('Edit'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _lead.notes?.isNotEmpty == true
                          ? _lead.notes!
                          : 'No notes added yet',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: _lead.notes?.isNotEmpty == true
                                ? null
                                : Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Status Update Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Update Status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      children: LeadStatus.values.map((status) {
                        final isSelected = _lead.status == status;
                        return FilterChip(
                          label: Text(_getStatusDisplayName(status)),
                          selected: isSelected,
                          onSelected: (selected) {
                            if (selected && !isSelected) {
                              _updateStatus(status);
                            }
                          },
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _makeCall(String phone) {
    launchUrl(Uri.parse('tel:$phone'));
  }

  void _sendSMS(String phone) {
    launchUrl(Uri.parse('sms:$phone'));
  }

  void _openWhatsApp(String phone) {
    launchUrl(Uri.parse('https://wa.me/$phone'));
  }

  void _sendEmail(String email) {
    launchUrl(Uri.parse('mailto:$email'));
  }

  void _updateStatus(LeadStatus status) {
    final updatedLead = _lead.copyWith(
      status: status,
      updatedAt: DateTime.now(),
    );

    context.read<LeadProvider>().updateLead(updatedLead);

    setState(() {
      _lead = updatedLead;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Status updated to ${_getStatusDisplayName(status)}'),
      ),
    );
  }

  void _showEditDialog(BuildContext context) {
    final nameController = TextEditingController(text: _lead.name);
    final phoneController = TextEditingController(text: _lead.phone);
    final emailController = TextEditingController(text: _lead.email ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Lead'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: phoneController,
                keyboardType: TextInputType.phone,
                decoration: const InputDecoration(
                  labelText: 'Phone',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final updatedLead = _lead.copyWith(
                name: nameController.text,
                phone: phoneController.text,
                email: emailController.text.isNotEmpty
                    ? emailController.text
                    : null,
                updatedAt: DateTime.now(),
              );

              context.read<LeadProvider>().updateLead(updatedLead);

              setState(() {
                _lead = updatedLead;
              });

              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showEditNotesDialog(BuildContext context) {
    _notesController.text = _lead.notes ?? '';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Notes'),
        content: TextField(
          controller: _notesController,
          maxLines: 5,
          decoration: const InputDecoration(
            hintText: 'Enter notes...',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final updatedLead = _lead.copyWith(
                notes: _notesController.text.isNotEmpty
                    ? _notesController.text
                    : null,
                updatedAt: DateTime.now(),
              );

              context.read<LeadProvider>().updateLead(updatedLead);

              setState(() {
                _lead = updatedLead;
              });

              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(LeadStatus status) {
    switch (status) {
      case LeadStatus.new_lead:
        return Colors.blue;
      case LeadStatus.contacted:
        return Colors.orange;
      case LeadStatus.qualified:
        return Colors.purple;
      case LeadStatus.proposal:
        return Colors.indigo;
      case LeadStatus.negotiation:
        return Colors.amber;
      case LeadStatus.closed_won:
        return Colors.green;
      case LeadStatus.closed_lost:
        return Colors.red;
      case LeadStatus.follow_up:
        return Colors.teal;
    }
  }

  String _getStatusDisplayName(LeadStatus status) {
    switch (status) {
      case LeadStatus.new_lead:
        return 'New Lead';
      case LeadStatus.contacted:
        return 'Contacted';
      case LeadStatus.qualified:
        return 'Qualified';
      case LeadStatus.proposal:
        return 'Proposal';
      case LeadStatus.negotiation:
        return 'Negotiation';
      case LeadStatus.closed_won:
        return 'Closed Won';
      case LeadStatus.closed_lost:
        return 'Closed Lost';
      case LeadStatus.follow_up:
        return 'Follow-up';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 32, color: Theme.of(context).primaryColor),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }
}
