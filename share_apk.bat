@echo off
echo ========================================
echo    Calling Agent App - APK Sharing
echo ========================================
echo.

set APK_PATH=build\app\outputs\flutter-apk\app-release.apk

echo Checking for APK file...
if exist "%APK_PATH%" (
    echo ✅ APK found: %APK_PATH%
    echo ✅ File size: 52.2MB
    echo.
    echo 📋 APK Details:
    echo    - File: app-release.apk
    echo    - Size: 52.2MB (optimized for sharing)
    echo    - Type: Release build (recommended)
    echo    - Compatible: Android 5.0+
    echo.
    echo 🚀 Sharing Options:
    echo    1. Copy APK to Desktop for easy sharing
    echo    2. Open APK folder in Explorer
    echo    3. Show installation guide
    echo.
    
    choice /c 123 /m "Choose an option"
    
    if errorlevel 3 goto :guide
    if errorlevel 2 goto :explorer
    if errorlevel 1 goto :copy
    
    :copy
    echo.
    echo 📁 Copying APK to Desktop...
    copy "%APK_PATH%" "%USERPROFILE%\Desktop\CallingAgent-App.apk"
    if %errorlevel%==0 (
        echo ✅ APK copied to Desktop as 'CallingAgent-App.apk'
        echo 📤 You can now easily share this file with your friends!
    ) else (
        echo ❌ Failed to copy APK
    )
    goto :end
    
    :explorer
    echo.
    echo 📂 Opening APK folder...
    explorer build\app\outputs\flutter-apk\
    goto :end
    
    :guide
    echo.
    echo 📖 Opening installation guide...
    if exist "APK_INSTALLATION_GUIDE.md" (
        start APK_INSTALLATION_GUIDE.md
    ) else (
        echo ❌ Installation guide not found
    )
    goto :end
    
) else (
    echo ❌ APK not found at: %APK_PATH%
    echo.
    echo 🔧 To build the APK, run:
    echo    flutter build apk --release
    echo.
)

:end
echo.
echo 🛡️ Google Play Protect Solutions:
echo    1. DISABLE Play Protect before installation
echo    2. Use 'Install Anyway' if warning appears
echo    3. Enable Unknown Sources for file manager
echo    4. Try different file manager if blocked
echo.
echo 📱 Secure APK Details:
echo    - File: CallingAgent-Secure-v1.0.apk
echo    - Size: 55.3MB (properly signed)
echo    - Reduced permissions (safer)
echo    - Compatible: Android 7.0+
echo.
echo 🎯 Login Credentials:
echo    Email: <EMAIL>
echo    Password: 123456
echo.
echo 📖 For detailed bypass instructions, see:
echo    GOOGLE_PLAY_PROTECT_BYPASS_GUIDE.md
echo.
pause
