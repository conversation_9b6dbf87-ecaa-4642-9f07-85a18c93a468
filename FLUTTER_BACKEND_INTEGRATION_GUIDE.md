# 📱 Flutter Backend Integration Guide - Calling Agent App

## 🎯 **Integration Overview**

This guide provides specific implementation details for integrating the Flutter Calling Agent app with the new backend API system. It includes code examples, data models, and sync strategies.

## 🔧 **Required Flutter Dependencies**

Add these to your `pubspec.yaml`:

```yaml
dependencies:
  # HTTP client
  dio: ^5.3.2
  
  # State management
  provider: ^6.0.5
  
  # Local storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # JSON serialization
  json_annotation: ^4.8.1
  
  # Secure storage
  flutter_secure_storage: ^9.0.0
  
  # Push notifications
  firebase_messaging: ^14.7.6
  
  # Network connectivity
  connectivity_plus: ^5.0.2

dev_dependencies:
  # Code generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1
```

## 🏗️ **Updated Data Models**

### **User Model with Server Integration:**

```dart
@HiveType(typeId: 0)
@JsonSerializable()
class UserModel {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String email;
  
  @HiveField(2)
  final String firstName;
  
  @HiveField(3)
  final String lastName;
  
  @HiveField(4)
  final String role; // 'channel_partner' or 'agent'
  
  @HiveField(5)
  final String? companyName;
  
  @HiveField(6)
  final String? phone;
  
  @HiveField(7)
  final DateTime? lastLogin;
  
  @HiveField(8)
  final bool isActive;

  UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
    this.companyName,
    this.phone,
    this.lastLogin,
    this.isActive = true,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}
```

### **Enhanced Lead Model:**

```dart
@HiveType(typeId: 1)
@JsonSerializable()
class LeadModel {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String phone;
  
  @HiveField(3)
  final String? email;
  
  @HiveField(4)
  final String? company;
  
  @HiveField(5)
  final LeadStatus status;
  
  @HiveField(6)
  final String source;
  
  @HiveField(7)
  final String? notes;
  
  @HiveField(8)
  final double? estimatedValue;
  
  @HiveField(9)
  final DateTime? followUpDate;
  
  @HiveField(10)
  final DateTime createdAt;
  
  @HiveField(11)
  final DateTime updatedAt;
  
  @HiveField(12)
  final String channelPartnerId;
  
  @HiveField(13)
  final String? assignedAgentId;
  
  @HiveField(14)
  final DateTime? assignedAt;
  
  @HiveField(15)
  final DateTime? lastContacted;
  
  @HiveField(16)
  final bool isSynced;
  
  @HiveField(17)
  final DateTime? lastSyncAt;

  LeadModel({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    this.company,
    required this.status,
    required this.source,
    this.notes,
    this.estimatedValue,
    this.followUpDate,
    required this.createdAt,
    required this.updatedAt,
    required this.channelPartnerId,
    this.assignedAgentId,
    this.assignedAt,
    this.lastContacted,
    this.isSynced = false,
    this.lastSyncAt,
  });

  factory LeadModel.fromJson(Map<String, dynamic> json) => _$LeadModelFromJson(json);
  Map<String, dynamic> toJson() => _$LeadModelToJson(this);
  
  LeadModel copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? company,
    LeadStatus? status,
    String? source,
    String? notes,
    double? estimatedValue,
    DateTime? followUpDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? channelPartnerId,
    String? assignedAgentId,
    DateTime? assignedAt,
    DateTime? lastContacted,
    bool? isSynced,
    DateTime? lastSyncAt,
  }) {
    return LeadModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      company: company ?? this.company,
      status: status ?? this.status,
      source: source ?? this.source,
      notes: notes ?? this.notes,
      estimatedValue: estimatedValue ?? this.estimatedValue,
      followUpDate: followUpDate ?? this.followUpDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      channelPartnerId: channelPartnerId ?? this.channelPartnerId,
      assignedAgentId: assignedAgentId ?? this.assignedAgentId,
      assignedAt: assignedAt ?? this.assignedAt,
      lastContacted: lastContacted ?? this.lastContacted,
      isSynced: isSynced ?? this.isSynced,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
    );
  }
}
```

## 🌐 **API Service Implementation**

### **Base API Service:**

```dart
class ApiService {
  static const String baseUrl = 'https://your-api-domain.com/api/v1';
  late final Dio _dio;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _secureStorage.read(key: 'access_token');
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          await _refreshToken();
          // Retry the request
          final token = await _secureStorage.read(key: 'access_token');
          error.requestOptions.headers['Authorization'] = 'Bearer $token';
          final response = await _dio.fetch(error.requestOptions);
          handler.resolve(response);
        } else {
          handler.next(error);
        }
      },
    ));
  }

  Future<void> _refreshToken() async {
    try {
      final refreshToken = await _secureStorage.read(key: 'refresh_token');
      if (refreshToken == null) throw Exception('No refresh token');

      final response = await _dio.post('/auth/refresh', data: {
        'refresh_token': refreshToken,
      });

      if (response.data['success']) {
        await _secureStorage.write(
          key: 'access_token',
          value: response.data['data']['access_token'],
        );
        await _secureStorage.write(
          key: 'refresh_token',
          value: response.data['data']['refresh_token'],
        );
      }
    } catch (e) {
      // Refresh failed, redirect to login
      await _secureStorage.deleteAll();
      throw Exception('Session expired');
    }
  }

  // Authentication methods
  Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await _dio.post('/auth/login', data: {
      'email': email,
      'password': password,
    });

    if (response.data['success']) {
      await _secureStorage.write(
        key: 'access_token',
        value: response.data['data']['access_token'],
      );
      await _secureStorage.write(
        key: 'refresh_token',
        value: response.data['data']['refresh_token'],
      );
    }

    return response.data;
  }

  Future<void> logout() async {
    try {
      await _dio.post('/auth/logout');
    } finally {
      await _secureStorage.deleteAll();
    }
  }

  // Lead management methods
  Future<List<LeadModel>> getLeads({
    String? status,
    int page = 1,
    int limit = 20,
    DateTime? lastSync,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'limit': limit,
    };

    if (status != null) queryParams['status'] = status;
    if (lastSync != null) queryParams['last_sync'] = lastSync.toIso8601String();

    final response = await _dio.get('/leads', queryParameters: queryParams);

    if (response.data['success']) {
      final List<dynamic> leadsJson = response.data['data'];
      return leadsJson.map((json) => LeadModel.fromJson(json)).toList();
    }

    throw Exception('Failed to fetch leads');
  }

  Future<LeadModel> createLead(LeadModel lead) async {
    final response = await _dio.post('/leads', data: lead.toJson());

    if (response.data['success']) {
      return LeadModel.fromJson(response.data['data']);
    }

    throw Exception('Failed to create lead');
  }

  Future<LeadModel> updateLead(LeadModel lead) async {
    final response = await _dio.put('/leads/${lead.id}', data: lead.toJson());

    if (response.data['success']) {
      return LeadModel.fromJson(response.data['data']);
    }

    throw Exception('Failed to update lead');
  }

  Future<void> updateLeadStatus(String leadId, LeadStatus status, String? notes) async {
    final response = await _dio.put('/leads/$leadId/status', data: {
      'status': status.toString().split('.').last,
      'notes': notes,
    });

    if (!response.data['success']) {
      throw Exception('Failed to update lead status');
    }
  }

  // Call log sync methods
  Future<void> syncCallLogs(List<CallLogModel> callLogs) async {
    final response = await _dio.post('/call-logs/sync', data: {
      'call_logs': callLogs.map((log) => log.toJson()).toList(),
    });

    if (!response.data['success']) {
      throw Exception('Failed to sync call logs');
    }
  }

  Future<LeadModel> createLeadFromCall(String callLogId, LeadModel lead) async {
    final response = await _dio.post('/call-logs/$callLogId/create-lead', 
      data: lead.toJson());

    if (response.data['success']) {
      return LeadModel.fromJson(response.data['data']);
    }

    throw Exception('Failed to create lead from call');
  }
}
```

## 🔄 **Sync Manager Implementation**

```dart
class SyncManager {
  final ApiService _apiService;
  final LocalStorageService _localStorage;
  final ConnectivityService _connectivity;

  SyncManager(this._apiService, this._localStorage, this._connectivity);

  Future<void> performFullSync() async {
    if (!await _connectivity.isConnected()) {
      throw Exception('No internet connection');
    }

    try {
      // Sync leads
      await _syncLeads();
      
      // Sync call logs
      await _syncCallLogs();
      
      // Update last sync timestamp
      await _localStorage.setLastSyncTime(DateTime.now());
      
    } catch (e) {
      debugPrint('Sync failed: $e');
      rethrow;
    }
  }

  Future<void> _syncLeads() async {
    // Get last sync time
    final lastSync = await _localStorage.getLastSyncTime();
    
    // Download new/updated leads from server
    final serverLeads = await _apiService.getLeads(lastSync: lastSync);
    
    // Get local unsynced leads
    final localLeads = await _localStorage.getUnsyncedLeads();
    
    // Upload local leads to server
    for (final lead in localLeads) {
      try {
        final syncedLead = await _apiService.createLead(lead);
        await _localStorage.updateLead(syncedLead.copyWith(isSynced: true));
      } catch (e) {
        debugPrint('Failed to sync lead ${lead.id}: $e');
      }
    }
    
    // Save server leads locally
    for (final lead in serverLeads) {
      await _localStorage.saveLead(lead.copyWith(isSynced: true));
    }
  }

  Future<void> _syncCallLogs() async {
    final unsyncedCallLogs = await _localStorage.getUnsyncedCallLogs();
    
    if (unsyncedCallLogs.isNotEmpty) {
      await _apiService.syncCallLogs(unsyncedCallLogs);
      
      // Mark call logs as synced
      for (final callLog in unsyncedCallLogs) {
        await _localStorage.updateCallLog(
          callLog.copyWith(isSynced: true, syncedAt: DateTime.now())
        );
      }
    }
  }

  Future<void> syncLeadStatus(String leadId, LeadStatus status, String? notes) async {
    // Update locally first
    final lead = await _localStorage.getLeadById(leadId);
    if (lead != null) {
      final updatedLead = lead.copyWith(
        status: status,
        notes: notes,
        updatedAt: DateTime.now(),
        isSynced: false,
      );
      await _localStorage.updateLead(updatedLead);
    }

    // Try to sync with server
    if (await _connectivity.isConnected()) {
      try {
        await _apiService.updateLeadStatus(leadId, status, notes);
        
        // Mark as synced
        if (lead != null) {
          await _localStorage.updateLead(
            lead.copyWith(isSynced: true, lastSyncAt: DateTime.now())
          );
        }
      } catch (e) {
        debugPrint('Failed to sync lead status: $e');
        // Will be synced later during full sync
      }
    }
  }
}
```

## 📱 **Push Notification Integration**

```dart
class PushNotificationService {
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;

  Future<void> initialize() async {
    // Request permission
    await _messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    // Get FCM token
    final token = await _messaging.getToken();
    debugPrint('FCM Token: $token');
    
    // Send token to server
    // await _apiService.updateFCMToken(token);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle notification taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }

  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Foreground message: ${message.notification?.title}');
    
    // Show local notification or update UI
    if (message.data['type'] == 'lead_assigned') {
      // Trigger lead sync
      // _syncManager.performFullSync();
    }
  }

  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('Background message: ${message.notification?.title}');
  }

  void _handleNotificationTap(RemoteMessage message) {
    debugPrint('Notification tapped: ${message.data}');
    
    // Navigate to appropriate screen based on notification type
    if (message.data['type'] == 'lead_assigned') {
      // Navigate to leads screen
    }
  }
}
```

## 🔄 **Updated Provider Classes**

```dart
class LeadProvider extends ChangeNotifier {
  final ApiService _apiService;
  final LocalStorageService _localStorage;
  final SyncManager _syncManager;

  List<LeadModel> _leads = [];
  bool _isLoading = false;
  bool _isSyncing = false;

  List<LeadModel> get leads => _leads;
  bool get isLoading => _isLoading;
  bool get isSyncing => _isSyncing;
  
  List<LeadModel> get unsyncedLeads => 
    _leads.where((lead) => !lead.isSynced).toList();

  LeadProvider(this._apiService, this._localStorage, this._syncManager);

  Future<void> loadLeads() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Load from local storage first
      _leads = await _localStorage.getLeads();
      notifyListeners();

      // Try to sync with server
      await syncWithServer();
    } catch (e) {
      debugPrint('Error loading leads: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> syncWithServer() async {
    if (_isSyncing) return;

    _isSyncing = true;
    notifyListeners();

    try {
      await _syncManager.performFullSync();
      
      // Reload leads after sync
      _leads = await _localStorage.getLeads();
    } catch (e) {
      debugPrint('Sync failed: $e');
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  Future<void> updateLeadStatus(String leadId, LeadStatus status, String? notes) async {
    try {
      await _syncManager.syncLeadStatus(leadId, status, notes);
      
      // Update local list
      final index = _leads.indexWhere((lead) => lead.id == leadId);
      if (index != -1) {
        _leads[index] = _leads[index].copyWith(
          status: status,
          notes: notes,
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating lead status: $e');
      rethrow;
    }
  }

  Future<void> createLeadFromCall(CallLogModel callLog, LeadModel lead) async {
    try {
      // Create locally first
      final localLead = lead.copyWith(
        id: const Uuid().v4(),
        source: 'call_classification',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      await _localStorage.saveLead(localLead);
      _leads.insert(0, localLead);
      notifyListeners();

      // Try to sync with server
      if (await ConnectivityService.isConnected()) {
        try {
          final syncedLead = await _apiService.createLeadFromCall(
            callLog.id, 
            localLead
          );
          
          // Update local lead with server data
          await _localStorage.updateLead(syncedLead.copyWith(isSynced: true));
          
          final index = _leads.indexWhere((l) => l.id == localLead.id);
          if (index != -1) {
            _leads[index] = syncedLead.copyWith(isSynced: true);
            notifyListeners();
          }
        } catch (e) {
          debugPrint('Failed to sync new lead: $e');
          // Will be synced later
        }
      }
    } catch (e) {
      debugPrint('Error creating lead from call: $e');
      rethrow;
    }
  }
}
```

This integration guide provides the complete implementation details for connecting your Flutter app with the backend API system!
